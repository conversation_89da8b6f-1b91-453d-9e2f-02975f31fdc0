using System.Net;
using System.Text;
using System.Text.Json;
using System.Web;
using CommandGuard.Configuration;
using CommandGuard.Enums;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace CommandGuard.Services.Infrastructure;

/// <summary>
/// HTTP API服务实现类 - 简化版本，内联消息转换逻辑
/// 使用HttpListener提供HTTP监听服务
/// 接收外部POST消息并处理
/// 实现IDisposable确保资源正确释放
/// </summary>
public sealed class HttpApiService(
    ILogger<HttpApiService> logger,
    IMessageStorageService storageService) : IHttpApiService
{
    private HttpListener? _httpListener;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _listenerTask;

    /// <summary>
    /// 启动HTTP监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            const int port = 5000; // 固定使用端口5000

            _httpListener = new HttpListener();
            _httpListener.Prefixes.Add($@"http://localhost:{port}/");
            _httpListener.Prefixes.Add($@"http://127.0.0.1:{port}/");
            _httpListener.Prefixes.Add($@"http://+:{port}/"); // 监听所有IP

            _httpListener.Start();

            _cancellationTokenSource = new CancellationTokenSource();
            var combinedToken = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _cancellationTokenSource.Token).Token;

            // 初始化统计信息
            _listenerTask = ListenAsync(combinedToken);

            logger.LogInformation(@"HTTP API服务启动成功，监听端口: {Port}", port);
            await Task.CompletedTask;
        }
        catch (HttpListenerException ex) when (ex.ErrorCode == 183 || ex.ErrorCode == 32) // ERROR_ALREADY_EXISTS 或 ERROR_SHARING_VIOLATION
        {
            logger.LogError(@"端口5000已被其他程序占用，错误码: {ErrorCode}", ex.ErrorCode);
            await StopAsync(CancellationToken.None);

            // 获取占用端口的进程信息
            var occupyingProcess = GetProcessUsingPort(5000);
            var errorMessage = occupyingProcess != null
                ? $@"端口5000已被程序 '{occupyingProcess}' 占用，请关闭该程序后重新启动CommandGuard。"
                : @"端口5000已被其他程序占用，请关闭占用端口5000的程序后重新启动CommandGuard。";

            throw new InvalidOperationException(errorMessage, ex);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"启动HTTP API服务失败");
            await StopAsync(CancellationToken.None);
            throw new InvalidOperationException($@"启动HTTP API服务失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 停止HTTP监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation(@"正在停止HTTP API服务...");

            // 1. 首先取消所有操作
            if (_cancellationTokenSource != null)
            {
                await _cancellationTokenSource.CancelAsync();
            }

            // 2. 停止HTTP监听器
            if (_httpListener?.IsListening == true)
            {
                try
                {
                    _httpListener.Stop();
                    logger.LogDebug(@"HTTP监听器已停止");
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"停止HTTP监听器时发生异常");
                }
            }

            // 3. 等待监听任务完成，设置超时避免无限等待
            if (_listenerTask != null)
            {
                try
                {
                    using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                    using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                        cancellationToken, timeoutCts.Token);

                    await _listenerTask.WaitAsync(combinedCts.Token).ConfigureAwait(false);
                    logger.LogDebug(@"监听任务已完成");
                }
                catch (OperationCanceledException)
                {
                    logger.LogWarning(@"等待监听任务完成超时，强制继续清理");
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"等待监听任务完成时发生异常");
                }
            }

            // 4. 关闭并释放HTTP监听器
            if (_httpListener != null)
            {
                try
                {
                    _httpListener.Close();
                    logger.LogDebug(@"HTTP监听器已关闭");
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"关闭HTTP监听器时发生异常");
                }
                finally
                {
                    _httpListener = null;
                }
            }

            // 5. 释放取消令牌源
            if (_cancellationTokenSource != null)
            {
                try
                {
                    _cancellationTokenSource.Dispose();
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"释放取消令牌源时发生异常");
                }
                finally
                {
                    _cancellationTokenSource = null;
                }
            }

            // 6. 清理其他资源
            _listenerTask = null;

            // 7. 强制垃圾回收，确保资源被释放
            GC.Collect();
            GC.WaitForPendingFinalizers();

            logger.LogInformation(@"HTTP API服务已完全停止并清理资源");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"停止HTTP API服务时发生异常");

            // 即使出现异常，也要尝试清理资源
            try
            {
                _httpListener?.Close();
                _httpListener = null;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                _listenerTask = null;
            }
            catch
            {
                // 忽略清理时的异常
            }
        }
    }

    /// <summary>
    /// 监听HTTP请求的主循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ListenAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation(@"开始监听HTTP请求");

        while (!cancellationToken.IsCancellationRequested && _httpListener?.IsListening == true)
        {
            try
            {
                var context = await _httpListener.GetContextAsync().ConfigureAwait(false);

                // 在后台处理请求，不阻塞主循环
                _ = Task.Run(async () => await ProcessRequestAsync(context, cancellationToken)
                    .ConfigureAwait(false), cancellationToken);
            }
            catch (ObjectDisposedException)
            {
                // HttpListener已被释放，正常退出
                break;
            }
            catch (HttpListenerException ex) when (ex.ErrorCode == 995) // ERROR_OPERATION_ABORTED
            {
                // 操作被取消，正常退出
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, @"监听HTTP请求时发生异常");
                await Task.Delay(1000, cancellationToken).ConfigureAwait(false); // 短暂延迟后继续
            }
        }

        logger.LogInformation(@"HTTP请求监听已停止");
    }

    /// <summary>
    /// 处理单个HTTP请求
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ProcessRequestAsync(HttpListenerContext context, CancellationToken cancellationToken)
    {
        var request = context.Request;
        var response = context.Response;

        try
        {
            logger.LogInformation(@"收到HTTP请求: {Method} {Url} 来自 {RemoteEndPoint}",
                request.HttpMethod, request.Url, request.RemoteEndPoint);

            // 设置CORS头
            response.Headers.Add("Access-Control-Allow-Origin", "*");
            response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            // 处理OPTIONS预检请求
            if (request.HttpMethod == "OPTIONS")
            {
                response.StatusCode = 200;
                response.Close();
                return;
            }

            // 检查路径
            if (!request.Url?.AbsolutePath.Equals("/api/", StringComparison.OrdinalIgnoreCase) == true &&
                !request.Url?.AbsolutePath.Equals("/api", StringComparison.OrdinalIgnoreCase) == true)
            {
                await SendErrorResponseAsync(response, 404, @"端点不存在").ConfigureAwait(false);
                return;
            }

            // 只处理POST请求用于消息提交
            if (request.HttpMethod != "POST")
            {
                await SendErrorResponseAsync(response, 405, @"只支持POST方法").ConfigureAwait(false);
                return;
            }

            // 判断是否已经开始了游戏服务,如果还没开始则直接return
            if (!RuntimeConfiguration.IsGameServiceStarted)
            {
                await SendErrorResponseAsync(response, 503, @"游戏服务未启动").ConfigureAwait(false);
                return;
            }

            // 读取请求体
            string requestBody;
            using (var reader = new StreamReader(request.InputStream, Encoding.UTF8))
            {
                requestBody = await reader.ReadToEndAsync(cancellationToken).ConfigureAwait(false);
            }

            // 检查请求体是否为空
            if (string.IsNullOrWhiteSpace(requestBody))
            {
                await SendErrorResponseAsync(response, 400, @"请求体不能为空").ConfigureAwait(false);
                return;
            }

            // 过滤掉空消息,以及消息中带有@的消息
            if (string.IsNullOrWhiteSpace(requestBody) || requestBody.Contains("@"))
            {
                await SendErrorResponseAsync(response, 400, @"请求体异常").ConfigureAwait(false);
                return;
            }

            // 过滤掉包含脚本的消息
            if (requestBody.Contains("<script>"))
            {
                await SendErrorResponseAsync(response, 400, @"请求体中包含脚本").ConfigureAwait(false);
                return;
            }

            // 根据当前选择的平台处理消息
            await ProcessMessageAsync(requestBody, response).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理HTTP请求时发生异常");
            try
            {
                await SendErrorResponseAsync(response, 500, @"服务器内部错误").ConfigureAwait(false);
            }
            catch
            {
                // 忽略响应发送失败
            }
        }
    }

    /// <summary>
    /// 根据当前选择的平台处理消息转换和存储
    /// </summary>
    /// <param name="requestBody">请求体内容</param>
    /// <param name="response">HTTP响应</param>
    private async Task ProcessMessageAsync(string requestBody, HttpListenerResponse response)
    {
        try
        {
            // 获取当前选择的平台
            var selectedPlatform = RuntimeConfiguration.SelectedChatApp;
            logger.LogDebug(@"当前选择的平台: {Platform}", selectedPlatform);

            InternalMessage? internalMessage;
            string platformName;

            // 根据选择的平台处理消息
            switch (selectedPlatform)
            {
                case EnumChatApp.一起聊吧:
                    internalMessage = await ProcessOneChatMessage(requestBody);
                    platformName = @"OneChat";
                    break;

                case EnumChatApp.MyQQ:
                    internalMessage = await ProcessMyQqMessage(requestBody);
                    platformName = @"MyQQ";
                    break;

                default:
                    logger.LogError(@"不支持的平台: {Platform}", selectedPlatform);
                    await SendErrorResponseAsync(response, 400, $@"不支持的平台: {selectedPlatform}").ConfigureAwait(false);
                    return;
            }

            // 检查转换结果
            if (internalMessage == null)
            {
                logger.LogWarning(@"消息转换结果为空，平台: {Platform}", platformName);
                await SendErrorResponseAsync(response, 500, @"消息转换失败").ConfigureAwait(false);
                return;
            }

            // 过滤机器人自己的消息
            if (IsRobotMessage(internalMessage))
            {
                var contentPreview = string.IsNullOrEmpty(internalMessage.Content)
                    ? "[空内容]"
                    : internalMessage.Content.Length > 50
                        ? internalMessage.Content.Substring(0, 50) + "..."
                        : internalMessage.Content;

                logger.LogInformation(@"已过滤机器人自己的消息 - 账号: {Account}, 内容: {Content}, 平台: {Platform}",
                    internalMessage.Account, contentPreview, platformName);
                await SendJsonResponseAsync(response, 200, new { success = true, message = "机器人消息已过滤" }).ConfigureAwait(false);
                return;
            }

            // 存储到数据库
            var messageId = await storageService.SaveMessageAsync(internalMessage).ConfigureAwait(false);

            // 返回成功响应
            var successResponse = new
            {
                success = true,
                message = $@"{platformName}消息处理成功",
                data = new
                {
                    messageId,
                    externalId = internalMessage.ExternalMessageId,
                    platform = platformName,
                    account = internalMessage.Account,
                    receivedTime = DateTime.Now
                }
            };

            // 发送响应
            await SendJsonResponseAsync(response, 200, successResponse).ConfigureAwait(false);

            // 记录日志
            logger.LogInformation(@"成功处理{Platform}消息，内部ID: {MessageId}, 外部ID: {ExternalId}, 用户: {Account}",
                platformName, messageId, internalMessage.ExternalMessageId, internalMessage.Account);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理消息异常");
            try
            {
                await SendErrorResponseAsync(response, 500, @"服务器内部错误").ConfigureAwait(false);
            }
            catch
            {
                // 忽略响应发送失败的异常
            }
        }
    }

    #region 发送Http响应

    /// <summary>
    /// 发送JSON响应
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <param name="statusCode">状态码</param>
    /// <param name="data">响应数据</param>
    private async Task SendJsonResponseAsync(HttpListenerResponse response, int statusCode, object data)
    {
        try
        {
            response.StatusCode = statusCode;
            response.ContentType = @"application/json; charset=utf-8";

            var jsonResponse = JsonSerializer.Serialize(data, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            var buffer = Encoding.UTF8.GetBytes(jsonResponse);
            response.ContentLength64 = buffer.Length;

            await response.OutputStream.WriteAsync(buffer, 0, buffer.Length).ConfigureAwait(false);
            response.Close();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送JSON响应时发生异常");
            try
            {
                response.Close();
            }
            catch
            {
                // 忽略关闭响应时的异常
            }
        }
    }

    /// <summary>
    /// 发送错误响应
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <param name="statusCode">状态码</param>
    /// <param name="errorMessage">错误消息</param>
    private async Task SendErrorResponseAsync(HttpListenerResponse response, int statusCode, string errorMessage)
    {
        var errorResponse = new
        {
            success = false,
            message = errorMessage,
            timestamp = DateTime.Now
        };

        await SendJsonResponseAsync(response, statusCode, errorResponse).ConfigureAwait(false);
    }

    #endregion

    #region 内联消息转换方法

    /// <summary>
    /// 处理OneChat信息
    /// </summary>
    /// <param name="requestBody"></param>
    private async Task<InternalMessage?> ProcessOneChatMessage(string requestBody)
    {
        try
        {
            JObject jObject = JObject.Parse(requestBody);
            // string id = jObject["id"]!.ToString();
            string? uid = jObject["Uid"]?.ToString();
            string? content = jObject["Content"]?.ToString().Trim();
            string? type = jObject["Type"]?.ToString().Trim();
            // string time = jObject["CreatedAt"]!.ToString().Trim();

            // long timestamp = long.Parse(jObject["timestamp"]?.ToString().Trim()!);
            // 把13位Unix时间戳转位北京时间
            // DateTime dt = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime.ToLocalTime();

            if (type is "0" && !string.IsNullOrWhiteSpace(content))
            {
                // 定义接收消息
                // ReceiveMessage msg = new ReceiveMessage
                // {
                //     // Id = id,
                //     GroupId = "OneChat",
                //     Account = uid!,
                //     Content = content,
                //     MsgTime = Ai.GetTextLeft(time.Replace("T", " "), "+"),
                // };

                // 定义接收消息
                InternalMessage internalMessage = new InternalMessage
                {
                    Account = uid!,
                    Content = content,
                };
                return internalMessage;
            }
        }
        catch (JsonReaderException ex)
        {
            // await DbHelper.AddLogAsync(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 解析错误", ex.ToString());
            logger.LogError(ex, @"捕获 ProcessMyQqMessage JSON 解析错误");
        }
        catch (JsonSerializationException ex)
        {
            // await DbHelper.AddLogAsync(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 序列化错误", ex.ToString());
            logger.LogError(ex, @"捕获 ProcessMyQqMessage JSON 序列化错误");
        }
        catch (Exception ex)
        {
            // await DbHelper.AddLogAsync(EnumLogType.机器人, "捕获 ProcessMyQqMessage 错误", ex.ToString());
            logger.LogError(ex, @"捕获 ProcessMyQqMessage 错误");
            await Task.Delay(0);
        }

        return null;
    }

    /// <summary>
    /// 处理MyQQ信息
    /// </summary>
    /// <param name="requestBody"></param>
    private async Task<InternalMessage?> ProcessMyQqMessage(string requestBody)
    {
        try
        {
            /*
              * {
                   "MQ_robot": "750909115",
                   "MQ_type": 2,
                   "MQ_type_sub": 0,
                   "MQ_fromID": "49033342",
                   "MQ_fromQQ": "292329830",
                   "MQ_passiveQQ": "750909115",
                   "MQ_msg": "%E6%88%91%E9%9D%A0",
                   "MQ_msgSeq": "2000694914",
                   "MQ_msgID": "28018",
                   "MQ_msgData": "7E E6 78 7E 2C C1 F6 BB 2E 79 4B 8D 1E B5 51 CF 1E DC 00 52 00 00 00 1B 00 09 00 06 03 E9 20 05 31 F0 00 0A 00 04 00 00 00 00 00 0C 00 05 00 01 00 01 01 02 EC 30 7E 01 11 6C 99 66 00 00 6D 72 66 F2 73 D3 00 00 00 67 00 CA 00 01 01 00 00 00 00 00 00 00 4D 53 47 00 00 00 00 00 66 F2 73 D3 77 40 2E 82 00 00 00 00 0A 00 86 02 00 06 E5 AE 8B E4 BD 93 00 00 19 00 56 01 00 53 AA 02 50 08 00 50 00 60 00 68 00 80 01 00 88 01 00 9A 01 3F 08 05 20 C0 50 C8 01 00 F0 01 00 F8 01 00 90 02 00 98 03 00 A0 03 00 B0 03 00 C0 03 00 D0 03 00 E8 03 00 8A 04 02 10 35 90 04 00 B8 04 00 C0 04 00 CA 04 00 F8 04 80 80 08 88 05 00 80 06 00 0E 00 0E 01 00 04 00 00 00 00 07 00 04 00 00 00 01 01 00 09 01 00 06 E6 88 91 E9 9D A0 12 00 25 02 00 09 E6 9D A5 E6 97 A5 E5 8F 91 03 00 01 01 04 00 04 00 00 00 08 05 00 04 00 00 00 01 08 00 04 00 00 00 01",
                   "MQ_timestamp": "*********"
                 }
              */
            JObject jObject = JObject.Parse(requestBody);
            string? mqType = jObject["MQ_type"]?.ToString();
            string? mqFromId = jObject["MQ_fromID"]?.ToString().Trim();
            if (mqType is "2" && !string.IsNullOrWhiteSpace(mqFromId))
            {
                // 定义接收消息
                InternalMessage internalMessage = new InternalMessage
                {
                    Account = jObject["MQ_fromQQ"]?.ToString()!,
                    Content = HttpUtility.UrlDecode(jObject["MQ_msg"]?.ToString()!)
                };
                return internalMessage;
            }
        }
        catch (JsonReaderException ex)
        {
            // await DbHelper.AddLogAsync(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 解析错误", ex.ToString());
            logger.LogError(ex, @"捕获 ProcessMyQqMessage JSON 解析错误");
        }
        catch (JsonSerializationException ex)
        {
            // await DbHelper.AddLogAsync(EnumLogType.机器人, "捕获 ProcessMyQqMessage JSON 序列化错误", ex.ToString());
            logger.LogError(ex, @"捕获 ProcessMyQqMessage JSON 序列化错误");
        }
        catch (Exception ex)
        {
            // await DbHelper.AddLogAsync(EnumLogType.机器人, "捕获 ProcessMyQqMessage 错误", ex.ToString());
            logger.LogError(ex, @"捕获 ProcessMyQqMessage 错误");
            await Task.Delay(0);
        }

        return null;
    }

    #endregion

    #region 端口管理

    /// <summary>
    /// 获取占用指定端口的进程名称
    /// </summary>
    /// <param name="port">端口号</param>
    /// <returns>进程名称，如果无法获取则返回null</returns>
    private string? GetProcessUsingPort(int port)
    {
        try
        {
            using var process = new System.Diagnostics.Process();
            process.StartInfo.FileName = @"netstat";
            process.StartInfo.Arguments = @"-ano";
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.CreateNoWindow = true;

            process.Start();
            var output = process.StandardOutput.ReadToEnd();
            process.WaitForExit();

            var lines = output.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains($@":{port} ") && (line.Contains(@"LISTENING") || line.Contains(@"监听")))
                {
                    var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length > 0 && int.TryParse(parts[^1], out int pid))
                    {
                        try
                        {
                            var proc = System.Diagnostics.Process.GetProcessById(pid);
                            return $@"{proc.ProcessName} (PID: {pid})";
                        }
                        catch
                        {
                            return $@"PID: {pid}";
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, @"获取端口 {Port} 占用进程信息失败", port);
        }

        return null;
    }

    #endregion

    #region IDisposable Implementation

    private bool _disposed;

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    private void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                try
                {
                    // 同步停止服务，确保资源被正确清理
                    StopAsync().GetAwaiter().GetResult();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, @"在Dispose中停止HTTP API服务时发生异常");
                }
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// 判断消息是否来自机器人自己
    /// </summary>
    /// <param name="message">内部消息对象</param>
    /// <returns>如果是机器人消息返回true，否则返回false</returns>
    private static bool IsRobotMessage(InternalMessage message)
    {
        try
        {
            // 检查消息对象是否有效
            if (string.IsNullOrEmpty(message.Account))
            {
                return false;
            }

            // 获取机器人账号
            var robotAccount = RuntimeConfiguration.GetRobotAccount();

            // 如果机器人账号为空，说明还未初始化，暂时不过滤
            if (string.IsNullOrEmpty(robotAccount))
            {
                return false;
            }

            // 比较消息发送者账号与机器人账号（忽略大小写）
            var isRobotMessage = string.Equals(message.Account.Trim(), robotAccount.Trim(), StringComparison.OrdinalIgnoreCase);

            return isRobotMessage;
        }
        catch (Exception)
        {
            // 如果判断过程中出现异常，为了安全起见，不过滤消息
            return false;
        }
    }

    /// <summary>
    /// 析构函数，确保资源被释放
    /// </summary>
    ~HttpApiService()
    {
        Dispose(false);
    }

    #endregion
}
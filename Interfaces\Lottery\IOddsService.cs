using CommandGuard.Models;

namespace CommandGuard.Interfaces.Lottery;

/// <summary>
/// 赔率服务接口
/// 提供赔率配置的基础功能：初始化、读取、修改
/// </summary>
public interface IOddsService
{
    /// <summary>
    /// 初始化默认赔率配置
    /// 如果数据库中没有赔率配置，则创建默认配置
    /// </summary>
    /// <returns>是否初始化成功</returns>
    Task<bool> InitializeDefaultOddsAsync();

    /// <summary>
    /// 强制重置赔率配置为出厂默认值
    /// 清除所有现有配置并重新创建默认配置
    /// </summary>
    /// <returns>是否重置成功</returns>
    Task<bool> ResetToFactoryDefaultOddsAsync();

    /// <summary>
    /// 获取所有赔率配置
    /// </summary>
    /// <returns>赔率配置列表</returns>
    Task<List<OddsConfig>> GetAllOddsAsync();

    /// <summary>
    /// 根据投注项目获取赔率配置
    /// </summary>
    /// <param name="playItem">投注项目名称</param>
    /// <returns>赔率配置，如果不存在则返回null</returns>
    Task<OddsConfig?> GetOddsByPlayItemAsync(string playItem);

    /// <summary>
    /// 创建赔率配置
    /// </summary>
    /// <param name="oddsConfig">赔率配置</param>
    /// <returns>是否创建成功</returns>
    Task<bool> CreateOddsAsync(OddsConfig oddsConfig);

    /// <summary>
    /// 更新赔率配置
    /// </summary>
    /// <param name="oddsConfig">赔率配置</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateOddsAsync(OddsConfig oddsConfig);

    /// <summary>
    /// 删除赔率配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteOddsAsync(int id);

    /// <summary>
    /// 批量更新赔率配置
    /// </summary>
    /// <param name="oddsConfigs">赔率配置列表</param>
    /// <returns>是否更新成功</returns>
    Task<bool> BatchUpdateOddsAsync(List<OddsConfig> oddsConfigs);

    /// <summary>
    /// 检查赔率配置是否存在
    /// </summary>
    /// <param name="playItem">投注项目名称</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string playItem);

    /// <summary>
    /// 获取赔率配置数量
    /// </summary>
    /// <returns>配置数量</returns>
    Task<int> GetCountAsync();

    /// <summary>
    /// 获取支持的投注项目列表
    /// </summary>
    /// <returns>投注项目名称列表</returns>
    Task<List<string>> GetSupportedPlayItemsAsync();


}

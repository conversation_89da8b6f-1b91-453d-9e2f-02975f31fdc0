using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Lottery;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// 投注指令验证服务实现
/// 专门负责投注指令的解析和验证
/// </summary>
public sealed class BetCommandValidatorService(IOddsService oddsService, ILogger<BetCommandValidatorService> logger) : IBetCommandValidatorService
{
    /// <summary>
    /// 验证投注指令 - 投注格式和有效性检查
    ///
    /// 功能：
    /// - 解析投注指令的格式
    /// - 验证投注项目的有效性
    /// - 验证投注金额的合理性
    /// - 检查赔率配置是否存在
    ///
    /// 支持的投注格式：
    /// - 基础投注："大10"、"小20"、"单15"、"双25"
    /// - 特殊投注："豹子100"、"对子50"
    /// - 正码投注："1-50"、"2-30"、"3-40"、"4-60"
    /// - 念码投注："1念2-100"、"3念4-200"
    /// - 三门投注："123-150"、"234-180"
    ///
    /// 验证规则：
    /// - 投注项目必须在赔率配置中存在
    /// - 投注金额必须大于0
    /// - 指令格式必须正确
    /// </summary>
    /// <param name="command">投注指令字符串</param>
    /// <returns>验证结果：(是否有效, 投注项目, 投注金额, 错误信息)</returns>
    public async Task<(bool IsValid, string PlayItem, decimal Amount, string ErrorMessage)> ValidateBetCommandAsync(string command)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(command))
                return (false, string.Empty, 0, @"投注指令不能为空");

            var parts = command.Trim().Split('/', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2)
                return (false, string.Empty, 0, @"投注指令格式错误，应为：投注项目/金额");

            var playItem = parts[0].Trim();
            var amountText = parts[1].Trim();

            if (!decimal.TryParse(amountText, out var amount))
                return (false, playItem, 0, @"投注金额格式错误，金额必须是数字");

            if (amount <= 0)
                return (false, playItem, amount, @"投注金额必须大于0");

            var oddsConfig = await oddsService.GetOddsByPlayItemAsync(playItem);
            if (oddsConfig == null)
                return (false, playItem, amount, $@"不支持的投注项目：{playItem}");

            if (amount < oddsConfig.MinStake)
                return (false, playItem, amount, $@"投注金额不能小于最小限额：{oddsConfig.MinStake}，当前金额：{amount}");

            if (amount > oddsConfig.MaxStake)
                return (false, playItem, amount, $@"投注金额不能大于最大限额：{oddsConfig.MaxStake}，当前金额：{amount}");

            return (true, playItem, amount, string.Empty);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"验证投注指令失败: {Command}", command);
            return (false, string.Empty, 0, @"验证投注指令时发生错误");
        }
    }
}
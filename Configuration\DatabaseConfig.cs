namespace CommandGuard.Configuration;

/// <summary>
/// 数据库配置根类
/// 用于映射 database.json 配置文件的结构
/// 包含数据库连接和性能相关的所有配置
/// </summary>
public class DatabaseConfig
{
    /// <summary>
    /// 数据库连接字符串配置节
    /// 包含应用程序所需的各种数据库连接信息
    /// 支持多个数据库连接配置，便于不同环境的部署
    /// </summary>
    public ConnectionStrings ConnectionStrings { get; set; } = new();

    /// <summary>
    /// 数据库运行时配置节
    /// 包含数据库性能、监控、重试等配置
    /// </summary>
    public Database Database { get; set; } = new();
}

/// <summary>
/// 数据库连接字符串配置类
/// 用于管理应用程序的数据库连接信息
/// 支持多个数据库连接，便于复杂应用场景
/// </summary>
public class ConnectionStrings
{
    /// <summary>
    /// 默认数据库连接字符串
    /// 当前使用SQLite数据库，包含数据库文件路径和连接池配置
    /// 格式：Data Source=数据库文件路径;其他参数
    /// 示例：Data Source=Demo.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;
    /// </summary>
    public string DefaultConnection { get; set; } = string.Empty;
}

/// <summary>
/// 数据库运行时配置类
/// 包含数据库性能、监控、重试等相关配置
/// </summary>
public class Database
{
    /// <summary>
    /// 是否启用数据库结构自动同步功能
    /// true：FreeSql会自动根据实体类创建或更新数据库表结构
    /// false：需要手动管理数据库表结构
    /// 生产环境建议设置为false，开发环境可设置为true
    /// </summary>
    public bool EnableAutoSyncStructure { get; set; } = true;

    /// <summary>
    /// 是否启用SQL语句监控功能
    /// true：在控制台输出执行的SQL语句，便于开发调试
    /// false：不输出SQL语句，适用于生产环境
    /// 生产环境必须设置为false，避免敏感信息泄露
    /// </summary>
    public bool EnableSqlMonitoring { get; set; } = false;

    /// <summary>
    /// 是否启用性能监控功能
    /// true：记录SQL执行时间和性能指标
    /// false：不记录性能数据
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 数据库命令超时时间（秒）
    /// 默认值：30秒
    /// 超过此时间的数据库操作将被取消
    /// </summary>
    public int CommandTimeout { get; set; } = 30;

    /// <summary>
    /// 数据库操作最大重试次数
    /// 默认值：3次
    /// 当数据库操作失败时的重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 重试延迟时间（秒）
    /// 默认值：2秒
    /// 每次重试之间的等待时间
    /// </summary>
    public int RetryDelaySeconds { get; set; } = 2;

    /// <summary>
    /// 验证数据库配置的有效性
    /// </summary>
    /// <returns>验证结果和错误信息</returns>
    public (bool IsValid, string ErrorMessage) Validate()
    {
        if (CommandTimeout <= 0)
            return (false, @"数据库命令超时时间必须大于0");

        if (MaxRetryCount < 0)
            return (false, @"最大重试次数不能为负数");

        if (RetryDelaySeconds < 0)
            return (false, @"重试延迟时间不能为负数");

        return (true, string.Empty);
    }
}

﻿using CommandGuard.Configuration;

namespace CommandGuard.Forms;

partial class FormMain
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }

        if (disposing)
        {
            // 停止并释放定时器
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
            _currentIssueBetUpdateTimer?.Stop();
            _currentIssueBetUpdateTimer?.Dispose();
            _titleUpdateTimer?.Stop();
            _titleUpdateTimer?.Dispose();

            // 停止期号时间服务和其他后台任务
            try
            {
                RuntimeConfiguration.RobotServiceCancellation?.Cancel();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($@"取消后台任务时发生异常: {ex.Message}");
            }

            // 释放BindingSource资源
            _memberBindingSource?.Dispose();
            _depositBindingSource?.Dispose();
            _withdrawBindingSource?.Dispose();

            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }

        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    /// Required method for Designer support - do not modify
    /// the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        components = new System.ComponentModel.Container();
        System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormMain));
        statusStrip1 = new StatusStrip();
        toolStripStatusLabel1 = new ToolStripStatusLabel();
        toolStripStatusSeparator1 = new ToolStripSeparator();
        toolStripStatusLabel8 = new ToolStripStatusLabel();
        toolStripStatusLabel2 = new ToolStripStatusLabel();
        toolStripStatusLabel3 = new ToolStripStatusLabel();
        toolStripStatusSeparator2 = new ToolStripSeparator();
        toolStripStatusLabel9 = new ToolStripStatusLabel();
        toolStripStatusLabel4 = new ToolStripStatusLabel();
        toolStripStatusLabel5 = new ToolStripStatusLabel();
        toolStripStatusSeparator3 = new ToolStripSeparator();
        toolStripStatusLabel10 = new ToolStripStatusLabel();
        toolStripStatusLabel6 = new ToolStripStatusLabel();
        toolStripStatusLabel7 = new ToolStripStatusLabel();
        toolStripStatusLabel11 = new ToolStripStatusLabel();
        tabControl_Menu = new TabControl();
        tabPage_主界面 = new TabPage();
        tableLayoutPanel_Body = new TableLayoutPanel();
        tableLayoutPanel_Left = new TableLayoutPanel();
        panel4 = new Panel();
        button_StopBet = new Button();
        button_StartBet = new Button();
        button_StopService = new Button();
        button_StartService = new Button();
        panel2 = new Panel();
        label_总积分 = new Label();
        label_总人数 = new Label();
        label_番摊结果 = new Label();
        label10 = new Label();
        label_开奖号码 = new Label();
        label8 = new Label();
        label_收单状态 = new Label();
        label_开奖期数 = new Label();
        label_开奖期数标题 = new Label();
        label_封盘倒计时 = new Label();
        label_正在投注期数 = new Label();
        label_正在投注期数标题 = new Label();
        panel1 = new Panel();
        comboBox_WorkGroupId = new ComboBox();
        button_卡奖时手动开奖或退单 = new Button();
        button_撤销用户投注 = new Button();
        button_选择Q群 = new Button();
        button_开奖历史 = new Button();
        dgvMembers = new DataGridView();
        tableLayoutPanel_Right = new TableLayoutPanel();
        dgvDepositRequests = new DataGridView();
        dgvWithdrawRequests = new DataGridView();
        dgvCurrentIssueBets = new DataGridView();
        tabPage_指令限额 = new TabPage();
        dataGridView_指令限额 = new DataGridView();
        tabPage_投注记录 = new TabPage();
        tableLayoutPanel_投注记录 = new TableLayoutPanel();
        panel3 = new Panel();
        label_显示查询投注记录有效下注总额以及总盈亏 = new Label();
        button_根据选择条件查询投注记录 = new Button();
        checkBox_查询投注记录包含假人 = new CheckBox();
        textBox_根据账号查询投注记录 = new TextBox();
        label3 = new Label();
        textBox_根据期号查询投注记录 = new TextBox();
        label2 = new Label();
        numericUpDown_查询投注记录结束分钟 = new NumericUpDown();
        numericUpDown_查询投注记录结束小时 = new NumericUpDown();
        dateTimePicker_查询投注记录结束日期 = new DateTimePicker();
        label1 = new Label();
        numericUpDown_查询投注记录开始分钟 = new NumericUpDown();
        numericUpDown_查询投注记录开始小时 = new NumericUpDown();
        dateTimePicker_查询投注记录开始日期 = new DateTimePicker();
        label_查询投注记录开始时间 = new Label();
        dataGridView_投注记录 = new DataGridView();
        tabPage_上下分记录 = new TabPage();
        tableLayoutPanel1 = new TableLayoutPanel();
        panel5 = new Panel();
        comboBox__查询上下分记录类型 = new ComboBox();
        label_显示查询上下分统计数据 = new Label();
        button_根据条件查询上下分记录 = new Button();
        checkBox_查询上下分记录包含假人 = new CheckBox();
        textBox_查询上下分记录账号 = new TextBox();
        label5 = new Label();
        label6 = new Label();
        numericUpDown_查询上下分记录结束分钟 = new NumericUpDown();
        numericUpDown_查询上下分记录结束小时 = new NumericUpDown();
        dateTimePicker_查询上下分记录结束日期 = new DateTimePicker();
        label7 = new Label();
        numericUpDown_查询上下分记录开始分钟 = new NumericUpDown();
        numericUpDown_查询上下分记录开始小时 = new NumericUpDown();
        dateTimePicker_查询上下分记录开始日期 = new DateTimePicker();
        label9 = new Label();
        dataGridView_上下分记录 = new DataGridView();
        tabPage_输赢流水回水记录 = new TabPage();
        tableLayoutPanel2 = new TableLayoutPanel();
        panel6 = new Panel();
        button_OneKeyRebate = new Button();
        checkBox_OneKeyRebate = new CheckBox();
        button_根据条件查询输赢流水回水记录 = new Button();
        checkBox_查询输赢流水回水记录包含假人 = new CheckBox();
        textBox_查询输赢流水回水记录账号 = new TextBox();
        label11 = new Label();
        textBox_查询输赢流水回水记录期号 = new TextBox();
        label12 = new Label();
        numericUpDown_查询输赢流水回水记录结束分钟 = new NumericUpDown();
        numericUpDown_查询输赢流水回水记录结束小时 = new NumericUpDown();
        dateTimePicker_查询输赢流水回水记录结束日期 = new DateTimePicker();
        label13 = new Label();
        numericUpDown_查询输赢流水回水记录开始分钟 = new NumericUpDown();
        numericUpDown_查询输赢流水回水记录开始小时 = new NumericUpDown();
        dateTimePicker_查询输赢流水回水记录开始日期 = new DateTimePicker();
        label14 = new Label();
        dataGridView_输赢流水回水记录 = new DataGridView();
        tabPage_拉手返点 = new TabPage();
        tableLayoutPanel3 = new TableLayoutPanel();
        panel7 = new Panel();
        button_根据条件查询拉手返点 = new Button();
        textBox_拉手名称 = new TextBox();
        label15 = new Label();
        numericUpDown_查询拉手返点结束分钟 = new NumericUpDown();
        numericUpDown_查询拉手返点结束小时 = new NumericUpDown();
        dateTimePicker_查询拉手返点结束日期 = new DateTimePicker();
        label16 = new Label();
        numericUpDown_查询拉手返点开始分钟 = new NumericUpDown();
        numericUpDown_查询拉手返点开始小时 = new NumericUpDown();
        dateTimePicker_查询拉手返点开始日期 = new DateTimePicker();
        label17 = new Label();
        dataGridView_拉手返点 = new DataGridView();
        tabPage_全部设置 = new TabPage();
        panel_Setting = new Panel();
        groupBox_业务开关 = new GroupBox();
        checkBox_发送6路图 = new CheckBox();
        checkBox_发送7路图 = new CheckBox();
        checkBox_开启对冲 = new CheckBox();
        checkBox_假人自动上分 = new CheckBox();
        checkBox_自动回水 = new CheckBox();
        checkBox_开启图片背景 = new CheckBox();
        groupBox_业务参数 = new GroupBox();
        label_返点比例 = new Label();
        numericUpDown_返点比例 = new NumericUpDown();
        label_单期限额 = new Label();
        numericUpDown_单期限额 = new NumericUpDown();
        groupBox_系统管理 = new GroupBox();
        label_系统管理说明 = new Label();
        button_初始化出厂设置 = new Button();
        button_清除一切记录 = new Button();
        contextMenuStrip_Members = new ContextMenuStrip(components);
        toolStripMenuItem_RefreshData = new ToolStripMenuItem();
        toolStripMenuItem_MemberAccount = new ToolStripMenuItem();
        toolStripMenuItem_MemberNickName = new ToolStripMenuItem();
        toolStripMenuItem_MemberRemark = new ToolStripMenuItem();
        toolStripMenuItem_MemberTotalDeposit = new ToolStripMenuItem();
        toolStripMenuItem_MemberTotalWithdraw = new ToolStripMenuItem();
        toolStripSeparator1 = new ToolStripSeparator();
        toolStripMenuItem_OperateDeposit = new ToolStripMenuItem();
        toolStripMenuItem_OperateWithdraw = new ToolStripMenuItem();
        toolStripMenuItem_ModifyRebateRate = new ToolStripMenuItem();
        toolStripMenuItem_ModifyRemark = new ToolStripMenuItem();
        toolStripMenuItem_SetAgentParent = new ToolStripMenuItem();
        toolStripMenuItem_ToggleUserType = new ToolStripMenuItem();
        statusStrip1.SuspendLayout();
        tabControl_Menu.SuspendLayout();
        tabPage_主界面.SuspendLayout();
        tableLayoutPanel_Body.SuspendLayout();
        tableLayoutPanel_Left.SuspendLayout();
        panel4.SuspendLayout();
        panel2.SuspendLayout();
        panel1.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)dgvMembers).BeginInit();
        tableLayoutPanel_Right.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)dgvDepositRequests).BeginInit();
        ((System.ComponentModel.ISupportInitialize)dgvWithdrawRequests).BeginInit();
        ((System.ComponentModel.ISupportInitialize)dgvCurrentIssueBets).BeginInit();
        tabPage_指令限额.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)dataGridView_指令限额).BeginInit();
        tabPage_投注记录.SuspendLayout();
        tableLayoutPanel_投注记录.SuspendLayout();
        panel3.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询投注记录结束分钟).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询投注记录结束小时).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询投注记录开始分钟).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询投注记录开始小时).BeginInit();
        ((System.ComponentModel.ISupportInitialize)dataGridView_投注记录).BeginInit();
        tabPage_上下分记录.SuspendLayout();
        tableLayoutPanel1.SuspendLayout();
        panel5.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询上下分记录结束分钟).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询上下分记录结束小时).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询上下分记录开始分钟).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询上下分记录开始小时).BeginInit();
        ((System.ComponentModel.ISupportInitialize)dataGridView_上下分记录).BeginInit();
        tabPage_输赢流水回水记录.SuspendLayout();
        tableLayoutPanel2.SuspendLayout();
        panel6.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询输赢流水回水记录结束分钟).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询输赢流水回水记录结束小时).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询输赢流水回水记录开始分钟).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询输赢流水回水记录开始小时).BeginInit();
        ((System.ComponentModel.ISupportInitialize)dataGridView_输赢流水回水记录).BeginInit();
        tabPage_拉手返点.SuspendLayout();
        tableLayoutPanel3.SuspendLayout();
        panel7.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询拉手返点结束分钟).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询拉手返点结束小时).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询拉手返点开始分钟).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询拉手返点开始小时).BeginInit();
        ((System.ComponentModel.ISupportInitialize)dataGridView_拉手返点).BeginInit();
        tabPage_全部设置.SuspendLayout();
        panel_Setting.SuspendLayout();
        groupBox_业务开关.SuspendLayout();
        groupBox_业务参数.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_返点比例).BeginInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_单期限额).BeginInit();
        groupBox_系统管理.SuspendLayout();
        contextMenuStrip_Members.SuspendLayout();
        SuspendLayout();
        // 
        // statusStrip1
        // 
        statusStrip1.Items.AddRange(new ToolStripItem[] { toolStripStatusLabel1, toolStripStatusSeparator1, toolStripStatusLabel8, toolStripStatusLabel2, toolStripStatusLabel3, toolStripStatusSeparator2, toolStripStatusLabel9, toolStripStatusLabel4, toolStripStatusLabel5, toolStripStatusSeparator3, toolStripStatusLabel10, toolStripStatusLabel6, toolStripStatusLabel7, toolStripStatusLabel11 });
        statusStrip1.Location = new Point(0, 677);
        statusStrip1.Name = "statusStrip1";
        statusStrip1.Size = new Size(1100, 23);
        statusStrip1.TabIndex = 0;
        statusStrip1.Text = "statusStrip1";
        // 
        // toolStripStatusLabel1
        // 
        toolStripStatusLabel1.Name = "toolStripStatusLabel1";
        toolStripStatusLabel1.Size = new Size(63, 18);
        toolStripStatusLabel1.Text = "标签1内容";
        // 
        // toolStripStatusSeparator1
        // 
        toolStripStatusSeparator1.Name = "toolStripStatusSeparator1";
        toolStripStatusSeparator1.Size = new Size(6, 23);
        // 
        // toolStripStatusLabel8
        // 
        toolStripStatusLabel8.BorderSides = ToolStripStatusLabelBorderSides.Right;
        toolStripStatusLabel8.Name = "toolStripStatusLabel8";
        toolStripStatusLabel8.Size = new Size(156, 18);
        toolStripStatusLabel8.Spring = true;
        // 
        // toolStripStatusLabel2
        // 
        toolStripStatusLabel2.Name = "toolStripStatusLabel2";
        toolStripStatusLabel2.Size = new Size(63, 18);
        toolStripStatusLabel2.Text = "标签2内容";
        // 
        // toolStripStatusLabel3
        // 
        toolStripStatusLabel3.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
        toolStripStatusLabel3.ForeColor = Color.Red;
        toolStripStatusLabel3.Name = "toolStripStatusLabel3";
        toolStripStatusLabel3.Size = new Size(63, 18);
        toolStripStatusLabel3.Text = "标签3内容";
        // 
        // toolStripStatusSeparator2
        // 
        toolStripStatusSeparator2.Name = "toolStripStatusSeparator2";
        toolStripStatusSeparator2.Size = new Size(6, 23);
        // 
        // toolStripStatusLabel9
        // 
        toolStripStatusLabel9.BorderSides = ToolStripStatusLabelBorderSides.Right;
        toolStripStatusLabel9.Name = "toolStripStatusLabel9";
        toolStripStatusLabel9.Size = new Size(156, 18);
        toolStripStatusLabel9.Spring = true;
        // 
        // toolStripStatusLabel4
        // 
        toolStripStatusLabel4.Name = "toolStripStatusLabel4";
        toolStripStatusLabel4.Size = new Size(63, 18);
        toolStripStatusLabel4.Text = "标签4内容";
        // 
        // toolStripStatusLabel5
        // 
        toolStripStatusLabel5.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
        toolStripStatusLabel5.ForeColor = Color.Red;
        toolStripStatusLabel5.Name = "toolStripStatusLabel5";
        toolStripStatusLabel5.Size = new Size(63, 18);
        toolStripStatusLabel5.Text = "标签5内容";
        // 
        // toolStripStatusSeparator3
        // 
        toolStripStatusSeparator3.Name = "toolStripStatusSeparator3";
        toolStripStatusSeparator3.Size = new Size(6, 23);
        // 
        // toolStripStatusLabel10
        // 
        toolStripStatusLabel10.BorderSides = ToolStripStatusLabelBorderSides.Right;
        toolStripStatusLabel10.Name = "toolStripStatusLabel10";
        toolStripStatusLabel10.Size = new Size(156, 18);
        toolStripStatusLabel10.Spring = true;
        // 
        // toolStripStatusLabel6
        // 
        toolStripStatusLabel6.Name = "toolStripStatusLabel6";
        toolStripStatusLabel6.Size = new Size(63, 18);
        toolStripStatusLabel6.Text = "标签6内容";
        // 
        // toolStripStatusLabel7
        // 
        toolStripStatusLabel7.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
        toolStripStatusLabel7.ForeColor = Color.Red;
        toolStripStatusLabel7.Name = "toolStripStatusLabel7";
        toolStripStatusLabel7.Size = new Size(63, 18);
        toolStripStatusLabel7.Text = "标签7内容";
        // 
        // toolStripStatusLabel11
        // 
        toolStripStatusLabel11.BorderSides = ToolStripStatusLabelBorderSides.Left;
        toolStripStatusLabel11.Name = "toolStripStatusLabel11";
        toolStripStatusLabel11.Size = new Size(156, 18);
        toolStripStatusLabel11.Spring = true;
        // 
        // tabControl_Menu
        // 
        tabControl_Menu.Controls.Add(tabPage_主界面);
        tabControl_Menu.Controls.Add(tabPage_指令限额);
        tabControl_Menu.Controls.Add(tabPage_投注记录);
        tabControl_Menu.Controls.Add(tabPage_上下分记录);
        tabControl_Menu.Controls.Add(tabPage_输赢流水回水记录);
        tabControl_Menu.Controls.Add(tabPage_拉手返点);
        tabControl_Menu.Controls.Add(tabPage_全部设置);
        tabControl_Menu.Dock = DockStyle.Fill;
        tabControl_Menu.Location = new Point(0, 0);
        tabControl_Menu.Name = "tabControl_Menu";
        tabControl_Menu.SelectedIndex = 0;
        tabControl_Menu.Size = new Size(1100, 677);
        tabControl_Menu.TabIndex = 1;
        // 
        // tabPage_主界面
        // 
        tabPage_主界面.Controls.Add(tableLayoutPanel_Body);
        tabPage_主界面.Location = new Point(4, 26);
        tabPage_主界面.Name = "tabPage_主界面";
        tabPage_主界面.Padding = new Padding(3);
        tabPage_主界面.Size = new Size(1092, 647);
        tabPage_主界面.TabIndex = 0;
        tabPage_主界面.Text = "主界面";
        tabPage_主界面.UseVisualStyleBackColor = true;
        // 
        // tableLayoutPanel_Body
        // 
        tableLayoutPanel_Body.ColumnCount = 2;
        tableLayoutPanel_Body.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 57F));
        tableLayoutPanel_Body.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 43F));
        tableLayoutPanel_Body.Controls.Add(tableLayoutPanel_Left, 0, 0);
        tableLayoutPanel_Body.Controls.Add(tableLayoutPanel_Right, 1, 0);
        tableLayoutPanel_Body.Dock = DockStyle.Fill;
        tableLayoutPanel_Body.Location = new Point(3, 3);
        tableLayoutPanel_Body.Margin = new Padding(0);
        tableLayoutPanel_Body.Name = "tableLayoutPanel_Body";
        tableLayoutPanel_Body.RowCount = 1;
        tableLayoutPanel_Body.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
        tableLayoutPanel_Body.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
        tableLayoutPanel_Body.Size = new Size(1086, 641);
        tableLayoutPanel_Body.TabIndex = 0;
        // 
        // tableLayoutPanel_Left
        // 
        tableLayoutPanel_Left.ColumnCount = 1;
        tableLayoutPanel_Left.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
        tableLayoutPanel_Left.Controls.Add(panel4, 0, 3);
        tableLayoutPanel_Left.Controls.Add(panel2, 0, 1);
        tableLayoutPanel_Left.Controls.Add(panel1, 0, 0);
        tableLayoutPanel_Left.Controls.Add(dgvMembers, 0, 2);
        tableLayoutPanel_Left.Dock = DockStyle.Fill;
        tableLayoutPanel_Left.Location = new Point(0, 0);
        tableLayoutPanel_Left.Margin = new Padding(0);
        tableLayoutPanel_Left.Name = "tableLayoutPanel_Left";
        tableLayoutPanel_Left.RowCount = 4;
        tableLayoutPanel_Left.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
        tableLayoutPanel_Left.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));
        tableLayoutPanel_Left.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
        tableLayoutPanel_Left.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
        tableLayoutPanel_Left.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
        tableLayoutPanel_Left.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
        tableLayoutPanel_Left.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
        tableLayoutPanel_Left.Size = new Size(619, 641);
        tableLayoutPanel_Left.TabIndex = 0;
        // 
        // panel4
        // 
        panel4.Controls.Add(button_StopBet);
        panel4.Controls.Add(button_StartBet);
        panel4.Controls.Add(button_StopService);
        panel4.Controls.Add(button_StartService);
        panel4.Dock = DockStyle.Fill;
        panel4.Location = new Point(0, 606);
        panel4.Margin = new Padding(0);
        panel4.Name = "panel4";
        panel4.Size = new Size(619, 35);
        panel4.TabIndex = 4;
        // 
        // button_StopBet
        // 
        button_StopBet.Font = new Font("宋体", 9F);
        button_StopBet.ForeColor = Color.Black;
        button_StopBet.Location = new Point(480, 4);
        button_StopBet.Name = "button_StopBet";
        button_StopBet.Size = new Size(100, 30);
        button_StopBet.TabIndex = 10;
        button_StopBet.Text = "停止飞单";
        button_StopBet.UseVisualStyleBackColor = true;
        button_StopBet.Click += button_StopBet_Click;
        // 
        // button_StartBet
        // 
        button_StartBet.Font = new Font("宋体", 9F);
        button_StartBet.ForeColor = Color.Black;
        button_StartBet.Location = new Point(381, 4);
        button_StartBet.Name = "button_StartBet";
        button_StartBet.Size = new Size(100, 30);
        button_StartBet.TabIndex = 9;
        button_StartBet.Text = "开始飞单";
        button_StartBet.UseVisualStyleBackColor = true;
        button_StartBet.Click += button_StartBet_Click;
        // 
        // button_StopService
        // 
        button_StopService.Font = new Font("宋体", 9F);
        button_StopService.ForeColor = Color.Black;
        button_StopService.Location = new Point(280, 4);
        button_StopService.Name = "button_StopService";
        button_StopService.Size = new Size(100, 30);
        button_StopService.TabIndex = 8;
        button_StopService.Text = "停止游戏";
        button_StopService.UseVisualStyleBackColor = true;
        button_StopService.Click += button_StopService_Click;
        // 
        // button_StartService
        // 
        button_StartService.Font = new Font("宋体", 9F);
        button_StartService.ForeColor = Color.Black;
        button_StartService.Location = new Point(181, 4);
        button_StartService.Name = "button_StartService";
        button_StartService.Size = new Size(100, 30);
        button_StartService.TabIndex = 7;
        button_StartService.Text = "开始游戏";
        button_StartService.UseVisualStyleBackColor = true;
        button_StartService.Click += button_StartService_Click;
        // 
        // panel2
        // 
        panel2.Controls.Add(label_总积分);
        panel2.Controls.Add(label_总人数);
        panel2.Controls.Add(label_番摊结果);
        panel2.Controls.Add(label10);
        panel2.Controls.Add(label_开奖号码);
        panel2.Controls.Add(label8);
        panel2.Controls.Add(label_收单状态);
        panel2.Controls.Add(label_开奖期数);
        panel2.Controls.Add(label_开奖期数标题);
        panel2.Controls.Add(label_封盘倒计时);
        panel2.Controls.Add(label_正在投注期数);
        panel2.Controls.Add(label_正在投注期数标题);
        panel2.Dock = DockStyle.Fill;
        panel2.Location = new Point(0, 35);
        panel2.Margin = new Padding(0);
        panel2.Name = "panel2";
        panel2.Size = new Size(619, 100);
        panel2.TabIndex = 2;
        // 
        // label_总积分
        // 
        label_总积分.BackColor = Color.LightGray;
        label_总积分.Font = new Font("宋体", 9F, FontStyle.Bold);
        label_总积分.ForeColor = Color.Blue;
        label_总积分.Location = new Point(457, 51);
        label_总积分.Name = "label_总积分";
        label_总积分.Size = new Size(156, 25);
        label_总积分.TabIndex = 55;
        label_总积分.Text = "总积分:";
        label_总积分.TextAlign = ContentAlignment.MiddleLeft;
        // 
        // label_总人数
        // 
        label_总人数.BackColor = Color.LightGray;
        label_总人数.Font = new Font("宋体", 9F, FontStyle.Bold);
        label_总人数.ForeColor = Color.Black;
        label_总人数.Location = new Point(457, 16);
        label_总人数.Name = "label_总人数";
        label_总人数.Size = new Size(156, 25);
        label_总人数.TabIndex = 54;
        label_总人数.Text = "总人数:";
        label_总人数.TextAlign = ContentAlignment.MiddleLeft;
        // 
        // label_番摊结果
        // 
        label_番摊结果.BackColor = Color.White;
        label_番摊结果.BorderStyle = BorderStyle.FixedSingle;
        label_番摊结果.Font = new Font("宋体", 18F, FontStyle.Bold);
        label_番摊结果.ForeColor = Color.Blue;
        label_番摊结果.Location = new Point(364, 35);
        label_番摊结果.Name = "label_番摊结果";
        label_番摊结果.Size = new Size(85, 50);
        label_番摊结果.TabIndex = 51;
        label_番摊结果.Text = "-";
        label_番摊结果.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label10
        // 
        label10.BackColor = Color.FromArgb(0, 0, 192);
        label10.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label10.ForeColor = Color.White;
        label10.Location = new Point(364, 10);
        label10.Name = "label10";
        label10.Size = new Size(85, 25);
        label10.TabIndex = 50;
        label10.Text = "番摊";
        label10.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label_开奖号码
        // 
        label_开奖号码.BackColor = Color.White;
        label_开奖号码.BorderStyle = BorderStyle.FixedSingle;
        label_开奖号码.Font = new Font("宋体", 18F, FontStyle.Bold);
        label_开奖号码.ForeColor = Color.Blue;
        label_开奖号码.Location = new Point(273, 35);
        label_开奖号码.Name = "label_开奖号码";
        label_开奖号码.Size = new Size(85, 50);
        label_开奖号码.TabIndex = 49;
        label_开奖号码.Text = "--";
        label_开奖号码.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label8
        // 
        label8.BackColor = Color.FromArgb(0, 0, 192);
        label8.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label8.ForeColor = Color.White;
        label8.Location = new Point(273, 10);
        label8.Name = "label8";
        label8.Size = new Size(85, 25);
        label8.TabIndex = 48;
        label8.Text = "号码";
        label8.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label_收单状态
        // 
        label_收单状态.BackColor = Color.Cyan;
        label_收单状态.BorderStyle = BorderStyle.FixedSingle;
        label_收单状态.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label_收单状态.ForeColor = Color.Red;
        label_收单状态.Location = new Point(145, 59);
        label_收单状态.Name = "label_收单状态";
        label_收单状态.Size = new Size(121, 25);
        label_收单状态.TabIndex = 47;
        label_收单状态.Text = "停止收单";
        label_收单状态.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label_开奖期数
        // 
        label_开奖期数.BackColor = Color.White;
        label_开奖期数.BorderStyle = BorderStyle.FixedSingle;
        label_开奖期数.Font = new Font("宋体", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
        label_开奖期数.ForeColor = Color.Red;
        label_开奖期数.Location = new Point(145, 35);
        label_开奖期数.Name = "label_开奖期数";
        label_开奖期数.Size = new Size(121, 25);
        label_开奖期数.TabIndex = 46;
        label_开奖期数.Text = "0";
        label_开奖期数.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label_开奖期数标题
        // 
        label_开奖期数标题.BackColor = Color.Red;
        label_开奖期数标题.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label_开奖期数标题.ForeColor = Color.White;
        label_开奖期数标题.Location = new Point(145, 10);
        label_开奖期数标题.Name = "label_开奖期数标题";
        label_开奖期数标题.Size = new Size(121, 25);
        label_开奖期数标题.TabIndex = 45;
        label_开奖期数标题.Text = "开奖期数";
        label_开奖期数标题.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label_封盘倒计时
        // 
        label_封盘倒计时.BackColor = Color.LightGray;
        label_封盘倒计时.BorderStyle = BorderStyle.FixedSingle;
        label_封盘倒计时.Font = new Font("宋体", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 134);
        label_封盘倒计时.ForeColor = Color.Blue;
        label_封盘倒计时.Location = new Point(6, 59);
        label_封盘倒计时.Name = "label_封盘倒计时";
        label_封盘倒计时.Size = new Size(131, 25);
        label_封盘倒计时.TabIndex = 44;
        label_封盘倒计时.Text = "00:00:00";
        label_封盘倒计时.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label_正在投注期数
        // 
        label_正在投注期数.BackColor = Color.White;
        label_正在投注期数.BorderStyle = BorderStyle.FixedSingle;
        label_正在投注期数.Font = new Font("宋体", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
        label_正在投注期数.ForeColor = Color.Red;
        label_正在投注期数.Location = new Point(6, 35);
        label_正在投注期数.Name = "label_正在投注期数";
        label_正在投注期数.Size = new Size(131, 25);
        label_正在投注期数.TabIndex = 43;
        label_正在投注期数.Text = "0";
        label_正在投注期数.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label_正在投注期数标题
        // 
        label_正在投注期数标题.BackColor = Color.Red;
        label_正在投注期数标题.Font = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label_正在投注期数标题.ForeColor = Color.White;
        label_正在投注期数标题.Location = new Point(6, 10);
        label_正在投注期数标题.Name = "label_正在投注期数标题";
        label_正在投注期数标题.Size = new Size(131, 25);
        label_正在投注期数标题.TabIndex = 42;
        label_正在投注期数标题.Text = "正在投注期数";
        label_正在投注期数标题.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // panel1
        // 
        panel1.Controls.Add(comboBox_WorkGroupId);
        panel1.Controls.Add(button_卡奖时手动开奖或退单);
        panel1.Controls.Add(button_撤销用户投注);
        panel1.Controls.Add(button_选择Q群);
        panel1.Controls.Add(button_开奖历史);
        panel1.Dock = DockStyle.Fill;
        panel1.Location = new Point(0, 0);
        panel1.Margin = new Padding(0);
        panel1.Name = "panel1";
        panel1.Size = new Size(619, 35);
        panel1.TabIndex = 1;
        // 
        // comboBox_WorkGroupId
        // 
        comboBox_WorkGroupId.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox_WorkGroupId.FormattingEnabled = true;
        comboBox_WorkGroupId.Location = new Point(6, 5);
        comboBox_WorkGroupId.Name = "comboBox_WorkGroupId";
        comboBox_WorkGroupId.Size = new Size(197, 25);
        comboBox_WorkGroupId.TabIndex = 0;
        // 
        // button_卡奖时手动开奖或退单
        // 
        button_卡奖时手动开奖或退单.Location = new Point(290, 3);
        button_卡奖时手动开奖或退单.Name = "button_卡奖时手动开奖或退单";
        button_卡奖时手动开奖或退单.Size = new Size(146, 28);
        button_卡奖时手动开奖或退单.TabIndex = 2;
        button_卡奖时手动开奖或退单.Text = "卡奖时手动开奖或退单";
        button_卡奖时手动开奖或退单.UseVisualStyleBackColor = true;
        // 
        // button_撤销用户投注
        // 
        button_撤销用户投注.Location = new Point(523, 3);
        button_撤销用户投注.Name = "button_撤销用户投注";
        button_撤销用户投注.Size = new Size(90, 28);
        button_撤销用户投注.TabIndex = 53;
        button_撤销用户投注.Text = "撤销用户投注";
        button_撤销用户投注.UseVisualStyleBackColor = true;
        // 
        // button_选择Q群
        // 
        button_选择Q群.Location = new Point(212, 3);
        button_选择Q群.Name = "button_选择Q群";
        button_选择Q群.Size = new Size(75, 28);
        button_选择Q群.TabIndex = 1;
        button_选择Q群.Text = "选择Q群";
        button_选择Q群.UseVisualStyleBackColor = true;
        button_选择Q群.Click += button_选择Q群_Click;
        // 
        // button_开奖历史
        // 
        button_开奖历史.Location = new Point(453, 3);
        button_开奖历史.Name = "button_开奖历史";
        button_开奖历史.Size = new Size(69, 28);
        button_开奖历史.TabIndex = 52;
        button_开奖历史.Text = "开奖历史";
        button_开奖历史.UseVisualStyleBackColor = true;
        // 
        // dgvMembers
        // 
        dgvMembers.Dock = DockStyle.Fill;
        dgvMembers.Location = new Point(3, 138);
        dgvMembers.Name = "dgvMembers";
        dgvMembers.Size = new Size(613, 465);
        dgvMembers.TabIndex = 0;
        // 
        // tableLayoutPanel_Right
        // 
        tableLayoutPanel_Right.ColumnCount = 1;
        tableLayoutPanel_Right.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel_Right.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel_Right.Controls.Add(dgvDepositRequests, 0, 0);
        tableLayoutPanel_Right.Controls.Add(dgvWithdrawRequests, 0, 1);
        tableLayoutPanel_Right.Controls.Add(dgvCurrentIssueBets, 0, 2);
        tableLayoutPanel_Right.Dock = DockStyle.Fill;
        tableLayoutPanel_Right.Location = new Point(622, 3);
        tableLayoutPanel_Right.Name = "tableLayoutPanel_Right";
        tableLayoutPanel_Right.RowCount = 3;
        tableLayoutPanel_Right.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));
        tableLayoutPanel_Right.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));
        tableLayoutPanel_Right.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
        tableLayoutPanel_Right.Size = new Size(461, 635);
        tableLayoutPanel_Right.TabIndex = 1;
        // 
        // dgvDepositRequests
        // 
        dgvDepositRequests.Dock = DockStyle.Fill;
        dgvDepositRequests.Location = new Point(3, 3);
        dgvDepositRequests.Name = "dgvDepositRequests";
        dgvDepositRequests.Size = new Size(455, 94);
        dgvDepositRequests.TabIndex = 1;
        // 
        // dgvWithdrawRequests
        // 
        dgvWithdrawRequests.Dock = DockStyle.Fill;
        dgvWithdrawRequests.Location = new Point(3, 103);
        dgvWithdrawRequests.Name = "dgvWithdrawRequests";
        dgvWithdrawRequests.Size = new Size(455, 94);
        dgvWithdrawRequests.TabIndex = 2;
        // 
        // dgvCurrentIssueBets
        // 
        dgvCurrentIssueBets.Dock = DockStyle.Fill;
        dgvCurrentIssueBets.Location = new Point(3, 203);
        dgvCurrentIssueBets.Name = "dgvCurrentIssueBets";
        dgvCurrentIssueBets.Size = new Size(455, 429);
        dgvCurrentIssueBets.TabIndex = 3;
        // 
        // tabPage_指令限额
        // 
        tabPage_指令限额.Controls.Add(dataGridView_指令限额);
        tabPage_指令限额.Location = new Point(4, 26);
        tabPage_指令限额.Name = "tabPage_指令限额";
        tabPage_指令限额.Padding = new Padding(3);
        tabPage_指令限额.Size = new Size(1092, 647);
        tabPage_指令限额.TabIndex = 9;
        tabPage_指令限额.Text = "指令限额";
        tabPage_指令限额.UseVisualStyleBackColor = true;
        // 
        // dataGridView_指令限额
        // 
        dataGridView_指令限额.AllowUserToResizeRows = false;
        dataGridView_指令限额.Dock = DockStyle.Fill;
        dataGridView_指令限额.Location = new Point(3, 3);
        dataGridView_指令限额.Name = "dataGridView_指令限额";
        dataGridView_指令限额.Size = new Size(1086, 641);
        dataGridView_指令限额.TabIndex = 2;
        // 
        // tabPage_投注记录
        // 
        tabPage_投注记录.Controls.Add(tableLayoutPanel_投注记录);
        tabPage_投注记录.Location = new Point(4, 26);
        tabPage_投注记录.Name = "tabPage_投注记录";
        tabPage_投注记录.Padding = new Padding(3);
        tabPage_投注记录.Size = new Size(1092, 647);
        tabPage_投注记录.TabIndex = 2;
        tabPage_投注记录.Text = "投注记录";
        tabPage_投注记录.UseVisualStyleBackColor = true;
        // 
        // tableLayoutPanel_投注记录
        // 
        tableLayoutPanel_投注记录.ColumnCount = 1;
        tableLayoutPanel_投注记录.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel_投注记录.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel_投注记录.Controls.Add(panel3, 0, 0);
        tableLayoutPanel_投注记录.Controls.Add(dataGridView_投注记录, 0, 1);
        tableLayoutPanel_投注记录.Dock = DockStyle.Fill;
        tableLayoutPanel_投注记录.Location = new Point(3, 3);
        tableLayoutPanel_投注记录.Name = "tableLayoutPanel_投注记录";
        tableLayoutPanel_投注记录.RowCount = 2;
        tableLayoutPanel_投注记录.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));
        tableLayoutPanel_投注记录.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
        tableLayoutPanel_投注记录.Size = new Size(1086, 641);
        tableLayoutPanel_投注记录.TabIndex = 1;
        // 
        // panel3
        // 
        panel3.Controls.Add(label_显示查询投注记录有效下注总额以及总盈亏);
        panel3.Controls.Add(button_根据选择条件查询投注记录);
        panel3.Controls.Add(checkBox_查询投注记录包含假人);
        panel3.Controls.Add(textBox_根据账号查询投注记录);
        panel3.Controls.Add(label3);
        panel3.Controls.Add(textBox_根据期号查询投注记录);
        panel3.Controls.Add(label2);
        panel3.Controls.Add(numericUpDown_查询投注记录结束分钟);
        panel3.Controls.Add(numericUpDown_查询投注记录结束小时);
        panel3.Controls.Add(dateTimePicker_查询投注记录结束日期);
        panel3.Controls.Add(label1);
        panel3.Controls.Add(numericUpDown_查询投注记录开始分钟);
        panel3.Controls.Add(numericUpDown_查询投注记录开始小时);
        panel3.Controls.Add(dateTimePicker_查询投注记录开始日期);
        panel3.Controls.Add(label_查询投注记录开始时间);
        panel3.Dock = DockStyle.Fill;
        panel3.Location = new Point(3, 3);
        panel3.Name = "panel3";
        panel3.Size = new Size(1080, 74);
        panel3.TabIndex = 0;
        // 
        // label_显示查询投注记录有效下注总额以及总盈亏
        // 
        label_显示查询投注记录有效下注总额以及总盈亏.ForeColor = Color.Red;
        label_显示查询投注记录有效下注总额以及总盈亏.Location = new Point(706, 10);
        label_显示查询投注记录有效下注总额以及总盈亏.Name = "label_显示查询投注记录有效下注总额以及总盈亏";
        label_显示查询投注记录有效下注总额以及总盈亏.Size = new Size(371, 23);
        label_显示查询投注记录有效下注总额以及总盈亏.TabIndex = 14;
        label_显示查询投注记录有效下注总额以及总盈亏.Text = "有效下注总额: 总盈亏:";
        // 
        // button_根据选择条件查询投注记录
        // 
        button_根据选择条件查询投注记录.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        button_根据选择条件查询投注记录.Location = new Point(599, 6);
        button_根据选择条件查询投注记录.Name = "button_根据选择条件查询投注记录";
        button_根据选择条件查询投注记录.Size = new Size(80, 30);
        button_根据选择条件查询投注记录.TabIndex = 13;
        button_根据选择条件查询投注记录.Text = "查询";
        button_根据选择条件查询投注记录.UseVisualStyleBackColor = true;
        button_根据选择条件查询投注记录.Click += button_根据选择条件查询投注记录_Click;
        // 
        // checkBox_查询投注记录包含假人
        // 
        checkBox_查询投注记录包含假人.Location = new Point(603, 43);
        checkBox_查询投注记录包含假人.Name = "checkBox_查询投注记录包含假人";
        checkBox_查询投注记录包含假人.Size = new Size(77, 24);
        checkBox_查询投注记录包含假人.TabIndex = 12;
        checkBox_查询投注记录包含假人.Text = "包含假人";
        checkBox_查询投注记录包含假人.UseVisualStyleBackColor = true;
        // 
        // textBox_根据账号查询投注记录
        // 
        textBox_根据账号查询投注记录.BorderStyle = BorderStyle.FixedSingle;
        textBox_根据账号查询投注记录.Location = new Point(485, 9);
        textBox_根据账号查询投注记录.Name = "textBox_根据账号查询投注记录";
        textBox_根据账号查询投注记录.Size = new Size(100, 23);
        textBox_根据账号查询投注记录.TabIndex = 11;
        // 
        // label3
        // 
        label3.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label3.Location = new Point(437, 9);
        label3.Name = "label3";
        label3.Size = new Size(47, 23);
        label3.TabIndex = 10;
        label3.Text = "账号";
        label3.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // textBox_根据期号查询投注记录
        // 
        textBox_根据期号查询投注记录.BorderStyle = BorderStyle.FixedSingle;
        textBox_根据期号查询投注记录.Location = new Point(330, 10);
        textBox_根据期号查询投注记录.Name = "textBox_根据期号查询投注记录";
        textBox_根据期号查询投注记录.Size = new Size(100, 23);
        textBox_根据期号查询投注记录.TabIndex = 9;
        // 
        // label2
        // 
        label2.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label2.Location = new Point(282, 10);
        label2.Name = "label2";
        label2.Size = new Size(47, 23);
        label2.TabIndex = 8;
        label2.Text = "期数";
        label2.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // numericUpDown_查询投注记录结束分钟
        // 
        numericUpDown_查询投注记录结束分钟.Location = new Point(227, 41);
        numericUpDown_查询投注记录结束分钟.Name = "numericUpDown_查询投注记录结束分钟";
        numericUpDown_查询投注记录结束分钟.Size = new Size(43, 23);
        numericUpDown_查询投注记录结束分钟.TabIndex = 7;
        // 
        // numericUpDown_查询投注记录结束小时
        // 
        numericUpDown_查询投注记录结束小时.Location = new Point(178, 40);
        numericUpDown_查询投注记录结束小时.Name = "numericUpDown_查询投注记录结束小时";
        numericUpDown_查询投注记录结束小时.Size = new Size(43, 23);
        numericUpDown_查询投注记录结束小时.TabIndex = 6;
        // 
        // dateTimePicker_查询投注记录结束日期
        // 
        dateTimePicker_查询投注记录结束日期.Location = new Point(50, 40);
        dateTimePicker_查询投注记录结束日期.Name = "dateTimePicker_查询投注记录结束日期";
        dateTimePicker_查询投注记录结束日期.Size = new Size(123, 23);
        dateTimePicker_查询投注记录结束日期.TabIndex = 5;
        // 
        // label1
        // 
        label1.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label1.Location = new Point(4, 39);
        label1.Name = "label1";
        label1.Size = new Size(47, 23);
        label1.TabIndex = 4;
        label1.Text = "结束";
        label1.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // numericUpDown_查询投注记录开始分钟
        // 
        numericUpDown_查询投注记录开始分钟.Location = new Point(227, 10);
        numericUpDown_查询投注记录开始分钟.Name = "numericUpDown_查询投注记录开始分钟";
        numericUpDown_查询投注记录开始分钟.Size = new Size(43, 23);
        numericUpDown_查询投注记录开始分钟.TabIndex = 3;
        // 
        // numericUpDown_查询投注记录开始小时
        // 
        numericUpDown_查询投注记录开始小时.Location = new Point(178, 9);
        numericUpDown_查询投注记录开始小时.Name = "numericUpDown_查询投注记录开始小时";
        numericUpDown_查询投注记录开始小时.Size = new Size(43, 23);
        numericUpDown_查询投注记录开始小时.TabIndex = 2;
        // 
        // dateTimePicker_查询投注记录开始日期
        // 
        dateTimePicker_查询投注记录开始日期.Location = new Point(50, 9);
        dateTimePicker_查询投注记录开始日期.Name = "dateTimePicker_查询投注记录开始日期";
        dateTimePicker_查询投注记录开始日期.Size = new Size(123, 23);
        dateTimePicker_查询投注记录开始日期.TabIndex = 1;
        // 
        // label_查询投注记录开始时间
        // 
        label_查询投注记录开始时间.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label_查询投注记录开始时间.Location = new Point(4, 8);
        label_查询投注记录开始时间.Name = "label_查询投注记录开始时间";
        label_查询投注记录开始时间.Size = new Size(47, 23);
        label_查询投注记录开始时间.TabIndex = 0;
        label_查询投注记录开始时间.Text = "开始";
        label_查询投注记录开始时间.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // dataGridView_投注记录
        // 
        dataGridView_投注记录.Dock = DockStyle.Fill;
        dataGridView_投注记录.Location = new Point(3, 83);
        dataGridView_投注记录.Name = "dataGridView_投注记录";
        dataGridView_投注记录.Size = new Size(1080, 555);
        dataGridView_投注记录.TabIndex = 1;
        // 
        // tabPage_上下分记录
        // 
        tabPage_上下分记录.Controls.Add(tableLayoutPanel1);
        tabPage_上下分记录.Location = new Point(4, 26);
        tabPage_上下分记录.Name = "tabPage_上下分记录";
        tabPage_上下分记录.Padding = new Padding(3);
        tabPage_上下分记录.Size = new Size(1092, 647);
        tabPage_上下分记录.TabIndex = 3;
        tabPage_上下分记录.Text = "上下分记录";
        tabPage_上下分记录.UseVisualStyleBackColor = true;
        // 
        // tableLayoutPanel1
        // 
        tableLayoutPanel1.ColumnCount = 1;
        tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel1.Controls.Add(panel5, 0, 0);
        tableLayoutPanel1.Controls.Add(dataGridView_上下分记录, 0, 1);
        tableLayoutPanel1.Dock = DockStyle.Fill;
        tableLayoutPanel1.Location = new Point(3, 3);
        tableLayoutPanel1.Name = "tableLayoutPanel1";
        tableLayoutPanel1.RowCount = 2;
        tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));
        tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
        tableLayoutPanel1.Size = new Size(1086, 641);
        tableLayoutPanel1.TabIndex = 1;
        // 
        // panel5
        // 
        panel5.Controls.Add(comboBox__查询上下分记录类型);
        panel5.Controls.Add(label_显示查询上下分统计数据);
        panel5.Controls.Add(button_根据条件查询上下分记录);
        panel5.Controls.Add(checkBox_查询上下分记录包含假人);
        panel5.Controls.Add(textBox_查询上下分记录账号);
        panel5.Controls.Add(label5);
        panel5.Controls.Add(label6);
        panel5.Controls.Add(numericUpDown_查询上下分记录结束分钟);
        panel5.Controls.Add(numericUpDown_查询上下分记录结束小时);
        panel5.Controls.Add(dateTimePicker_查询上下分记录结束日期);
        panel5.Controls.Add(label7);
        panel5.Controls.Add(numericUpDown_查询上下分记录开始分钟);
        panel5.Controls.Add(numericUpDown_查询上下分记录开始小时);
        panel5.Controls.Add(dateTimePicker_查询上下分记录开始日期);
        panel5.Controls.Add(label9);
        panel5.Dock = DockStyle.Fill;
        panel5.Location = new Point(3, 3);
        panel5.Name = "panel5";
        panel5.Size = new Size(1080, 74);
        panel5.TabIndex = 0;
        // 
        // comboBox__查询上下分记录类型
        // 
        comboBox__查询上下分记录类型.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox__查询上下分记录类型.FormattingEnabled = true;
        comboBox__查询上下分记录类型.Items.AddRange(new object[] { "全部", "上分", "下分", "回水" });
        comboBox__查询上下分记录类型.Location = new Point(327, 9);
        comboBox__查询上下分记录类型.Name = "comboBox__查询上下分记录类型";
        comboBox__查询上下分记录类型.Size = new Size(100, 25);
        comboBox__查询上下分记录类型.TabIndex = 15;
        // 
        // label_显示查询上下分统计数据
        // 
        label_显示查询上下分统计数据.ForeColor = Color.Red;
        label_显示查询上下分统计数据.Location = new Point(706, 10);
        label_显示查询上下分统计数据.Name = "label_显示查询上下分统计数据";
        label_显示查询上下分统计数据.Size = new Size(371, 23);
        label_显示查询上下分统计数据.TabIndex = 14;
        label_显示查询上下分统计数据.Text = "上分总额: 下分总额: 回水总额: 总积分";
        // 
        // button_根据条件查询上下分记录
        // 
        button_根据条件查询上下分记录.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        button_根据条件查询上下分记录.Location = new Point(599, 6);
        button_根据条件查询上下分记录.Name = "button_根据条件查询上下分记录";
        button_根据条件查询上下分记录.Size = new Size(80, 30);
        button_根据条件查询上下分记录.TabIndex = 13;
        button_根据条件查询上下分记录.Text = "查询";
        button_根据条件查询上下分记录.UseVisualStyleBackColor = true;
        button_根据条件查询上下分记录.Click += button_根据条件查询上下分记录_Click;
        // 
        // checkBox_查询上下分记录包含假人
        // 
        checkBox_查询上下分记录包含假人.Location = new Point(603, 43);
        checkBox_查询上下分记录包含假人.Name = "checkBox_查询上下分记录包含假人";
        checkBox_查询上下分记录包含假人.Size = new Size(77, 24);
        checkBox_查询上下分记录包含假人.TabIndex = 12;
        checkBox_查询上下分记录包含假人.Text = "包含假人";
        checkBox_查询上下分记录包含假人.UseVisualStyleBackColor = true;
        // 
        // textBox_查询上下分记录账号
        // 
        textBox_查询上下分记录账号.BorderStyle = BorderStyle.FixedSingle;
        textBox_查询上下分记录账号.Location = new Point(485, 9);
        textBox_查询上下分记录账号.Name = "textBox_查询上下分记录账号";
        textBox_查询上下分记录账号.Size = new Size(100, 23);
        textBox_查询上下分记录账号.TabIndex = 11;
        // 
        // label5
        // 
        label5.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label5.Location = new Point(437, 9);
        label5.Name = "label5";
        label5.Size = new Size(47, 23);
        label5.TabIndex = 10;
        label5.Text = "账号";
        label5.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // label6
        // 
        label6.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label6.Location = new Point(282, 10);
        label6.Name = "label6";
        label6.Size = new Size(47, 23);
        label6.TabIndex = 8;
        label6.Text = "类型";
        label6.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // numericUpDown_查询上下分记录结束分钟
        // 
        numericUpDown_查询上下分记录结束分钟.Location = new Point(227, 41);
        numericUpDown_查询上下分记录结束分钟.Name = "numericUpDown_查询上下分记录结束分钟";
        numericUpDown_查询上下分记录结束分钟.Size = new Size(43, 23);
        numericUpDown_查询上下分记录结束分钟.TabIndex = 7;
        // 
        // numericUpDown_查询上下分记录结束小时
        // 
        numericUpDown_查询上下分记录结束小时.Location = new Point(178, 40);
        numericUpDown_查询上下分记录结束小时.Name = "numericUpDown_查询上下分记录结束小时";
        numericUpDown_查询上下分记录结束小时.Size = new Size(43, 23);
        numericUpDown_查询上下分记录结束小时.TabIndex = 6;
        // 
        // dateTimePicker_查询上下分记录结束日期
        // 
        dateTimePicker_查询上下分记录结束日期.Location = new Point(50, 40);
        dateTimePicker_查询上下分记录结束日期.Name = "dateTimePicker_查询上下分记录结束日期";
        dateTimePicker_查询上下分记录结束日期.Size = new Size(123, 23);
        dateTimePicker_查询上下分记录结束日期.TabIndex = 5;
        // 
        // label7
        // 
        label7.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label7.Location = new Point(4, 39);
        label7.Name = "label7";
        label7.Size = new Size(47, 23);
        label7.TabIndex = 4;
        label7.Text = "结束";
        label7.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // numericUpDown_查询上下分记录开始分钟
        // 
        numericUpDown_查询上下分记录开始分钟.Location = new Point(227, 10);
        numericUpDown_查询上下分记录开始分钟.Name = "numericUpDown_查询上下分记录开始分钟";
        numericUpDown_查询上下分记录开始分钟.Size = new Size(43, 23);
        numericUpDown_查询上下分记录开始分钟.TabIndex = 3;
        // 
        // numericUpDown_查询上下分记录开始小时
        // 
        numericUpDown_查询上下分记录开始小时.Location = new Point(178, 9);
        numericUpDown_查询上下分记录开始小时.Name = "numericUpDown_查询上下分记录开始小时";
        numericUpDown_查询上下分记录开始小时.Size = new Size(43, 23);
        numericUpDown_查询上下分记录开始小时.TabIndex = 2;
        // 
        // dateTimePicker_查询上下分记录开始日期
        // 
        dateTimePicker_查询上下分记录开始日期.Location = new Point(50, 9);
        dateTimePicker_查询上下分记录开始日期.Name = "dateTimePicker_查询上下分记录开始日期";
        dateTimePicker_查询上下分记录开始日期.Size = new Size(123, 23);
        dateTimePicker_查询上下分记录开始日期.TabIndex = 1;
        // 
        // label9
        // 
        label9.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label9.Location = new Point(4, 8);
        label9.Name = "label9";
        label9.Size = new Size(47, 23);
        label9.TabIndex = 0;
        label9.Text = "开始";
        label9.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // dataGridView_上下分记录
        // 
        dataGridView_上下分记录.Dock = DockStyle.Fill;
        dataGridView_上下分记录.Location = new Point(3, 83);
        dataGridView_上下分记录.Name = "dataGridView_上下分记录";
        dataGridView_上下分记录.Size = new Size(1080, 555);
        dataGridView_上下分记录.TabIndex = 1;
        // 
        // tabPage_输赢流水回水记录
        // 
        tabPage_输赢流水回水记录.Controls.Add(tableLayoutPanel2);
        tabPage_输赢流水回水记录.Location = new Point(4, 26);
        tabPage_输赢流水回水记录.Name = "tabPage_输赢流水回水记录";
        tabPage_输赢流水回水记录.Padding = new Padding(3);
        tabPage_输赢流水回水记录.Size = new Size(1092, 647);
        tabPage_输赢流水回水记录.TabIndex = 6;
        tabPage_输赢流水回水记录.Text = "输赢流水回水记录";
        tabPage_输赢流水回水记录.UseVisualStyleBackColor = true;
        // 
        // tableLayoutPanel2
        // 
        tableLayoutPanel2.ColumnCount = 1;
        tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel2.Controls.Add(panel6, 0, 0);
        tableLayoutPanel2.Controls.Add(dataGridView_输赢流水回水记录, 0, 1);
        tableLayoutPanel2.Dock = DockStyle.Fill;
        tableLayoutPanel2.Location = new Point(3, 3);
        tableLayoutPanel2.Name = "tableLayoutPanel2";
        tableLayoutPanel2.RowCount = 2;
        tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));
        tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
        tableLayoutPanel2.Size = new Size(1086, 641);
        tableLayoutPanel2.TabIndex = 3;
        // 
        // panel6
        // 
        panel6.Controls.Add(button_OneKeyRebate);
        panel6.Controls.Add(checkBox_OneKeyRebate);
        panel6.Controls.Add(button_根据条件查询输赢流水回水记录);
        panel6.Controls.Add(checkBox_查询输赢流水回水记录包含假人);
        panel6.Controls.Add(textBox_查询输赢流水回水记录账号);
        panel6.Controls.Add(label11);
        panel6.Controls.Add(textBox_查询输赢流水回水记录期号);
        panel6.Controls.Add(label12);
        panel6.Controls.Add(numericUpDown_查询输赢流水回水记录结束分钟);
        panel6.Controls.Add(numericUpDown_查询输赢流水回水记录结束小时);
        panel6.Controls.Add(dateTimePicker_查询输赢流水回水记录结束日期);
        panel6.Controls.Add(label13);
        panel6.Controls.Add(numericUpDown_查询输赢流水回水记录开始分钟);
        panel6.Controls.Add(numericUpDown_查询输赢流水回水记录开始小时);
        panel6.Controls.Add(dateTimePicker_查询输赢流水回水记录开始日期);
        panel6.Controls.Add(label14);
        panel6.Dock = DockStyle.Fill;
        panel6.Location = new Point(3, 3);
        panel6.Name = "panel6";
        panel6.Size = new Size(1080, 74);
        panel6.TabIndex = 0;
        // 
        // button_OneKeyRebate
        // 
        button_OneKeyRebate.Enabled = false;
        button_OneKeyRebate.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        button_OneKeyRebate.Location = new Point(887, 5);
        button_OneKeyRebate.Name = "button_OneKeyRebate";
        button_OneKeyRebate.Size = new Size(119, 30);
        button_OneKeyRebate.TabIndex = 15;
        button_OneKeyRebate.Text = "一键回水到账户";
        button_OneKeyRebate.UseVisualStyleBackColor = true;
        // 
        // checkBox_OneKeyRebate
        // 
        checkBox_OneKeyRebate.Location = new Point(864, 9);
        checkBox_OneKeyRebate.Name = "checkBox_OneKeyRebate";
        checkBox_OneKeyRebate.Size = new Size(17, 24);
        checkBox_OneKeyRebate.TabIndex = 14;
        checkBox_OneKeyRebate.UseVisualStyleBackColor = true;
        checkBox_OneKeyRebate.CheckedChanged += checkBox_OneKeyRebate_CheckedChanged;
        // 
        // button_根据条件查询输赢流水回水记录
        // 
        button_根据条件查询输赢流水回水记录.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        button_根据条件查询输赢流水回水记录.Location = new Point(599, 6);
        button_根据条件查询输赢流水回水记录.Name = "button_根据条件查询输赢流水回水记录";
        button_根据条件查询输赢流水回水记录.Size = new Size(66, 30);
        button_根据条件查询输赢流水回水记录.TabIndex = 13;
        button_根据条件查询输赢流水回水记录.Text = "查询";
        button_根据条件查询输赢流水回水记录.UseVisualStyleBackColor = true;
        button_根据条件查询输赢流水回水记录.Click += button_根据条件查询输赢流水回水记录_Click;
        // 
        // checkBox_查询输赢流水回水记录包含假人
        // 
        checkBox_查询输赢流水回水记录包含假人.Location = new Point(676, 9);
        checkBox_查询输赢流水回水记录包含假人.Name = "checkBox_查询输赢流水回水记录包含假人";
        checkBox_查询输赢流水回水记录包含假人.Size = new Size(77, 24);
        checkBox_查询输赢流水回水记录包含假人.TabIndex = 12;
        checkBox_查询输赢流水回水记录包含假人.Text = "包含假人";
        checkBox_查询输赢流水回水记录包含假人.UseVisualStyleBackColor = true;
        // 
        // textBox_查询输赢流水回水记录账号
        // 
        textBox_查询输赢流水回水记录账号.BorderStyle = BorderStyle.FixedSingle;
        textBox_查询输赢流水回水记录账号.Location = new Point(484, 10);
        textBox_查询输赢流水回水记录账号.Name = "textBox_查询输赢流水回水记录账号";
        textBox_查询输赢流水回水记录账号.Size = new Size(100, 23);
        textBox_查询输赢流水回水记录账号.TabIndex = 11;
        // 
        // label11
        // 
        label11.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label11.Location = new Point(436, 10);
        label11.Name = "label11";
        label11.Size = new Size(47, 23);
        label11.TabIndex = 10;
        label11.Text = "账号";
        label11.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // textBox_查询输赢流水回水记录期号
        // 
        textBox_查询输赢流水回水记录期号.BorderStyle = BorderStyle.FixedSingle;
        textBox_查询输赢流水回水记录期号.Location = new Point(330, 10);
        textBox_查询输赢流水回水记录期号.Name = "textBox_查询输赢流水回水记录期号";
        textBox_查询输赢流水回水记录期号.Size = new Size(100, 23);
        textBox_查询输赢流水回水记录期号.TabIndex = 9;
        // 
        // label12
        // 
        label12.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label12.Location = new Point(282, 10);
        label12.Name = "label12";
        label12.Size = new Size(47, 23);
        label12.TabIndex = 8;
        label12.Text = "期数";
        label12.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // numericUpDown_查询输赢流水回水记录结束分钟
        // 
        numericUpDown_查询输赢流水回水记录结束分钟.Location = new Point(227, 41);
        numericUpDown_查询输赢流水回水记录结束分钟.Name = "numericUpDown_查询输赢流水回水记录结束分钟";
        numericUpDown_查询输赢流水回水记录结束分钟.Size = new Size(43, 23);
        numericUpDown_查询输赢流水回水记录结束分钟.TabIndex = 7;
        // 
        // numericUpDown_查询输赢流水回水记录结束小时
        // 
        numericUpDown_查询输赢流水回水记录结束小时.Location = new Point(178, 40);
        numericUpDown_查询输赢流水回水记录结束小时.Name = "numericUpDown_查询输赢流水回水记录结束小时";
        numericUpDown_查询输赢流水回水记录结束小时.Size = new Size(43, 23);
        numericUpDown_查询输赢流水回水记录结束小时.TabIndex = 6;
        // 
        // dateTimePicker_查询输赢流水回水记录结束日期
        // 
        dateTimePicker_查询输赢流水回水记录结束日期.Location = new Point(50, 40);
        dateTimePicker_查询输赢流水回水记录结束日期.Name = "dateTimePicker_查询输赢流水回水记录结束日期";
        dateTimePicker_查询输赢流水回水记录结束日期.Size = new Size(123, 23);
        dateTimePicker_查询输赢流水回水记录结束日期.TabIndex = 5;
        // 
        // label13
        // 
        label13.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label13.Location = new Point(4, 39);
        label13.Name = "label13";
        label13.Size = new Size(47, 23);
        label13.TabIndex = 4;
        label13.Text = "结束";
        label13.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // numericUpDown_查询输赢流水回水记录开始分钟
        // 
        numericUpDown_查询输赢流水回水记录开始分钟.Location = new Point(227, 10);
        numericUpDown_查询输赢流水回水记录开始分钟.Name = "numericUpDown_查询输赢流水回水记录开始分钟";
        numericUpDown_查询输赢流水回水记录开始分钟.Size = new Size(43, 23);
        numericUpDown_查询输赢流水回水记录开始分钟.TabIndex = 3;
        // 
        // numericUpDown_查询输赢流水回水记录开始小时
        // 
        numericUpDown_查询输赢流水回水记录开始小时.Location = new Point(178, 9);
        numericUpDown_查询输赢流水回水记录开始小时.Name = "numericUpDown_查询输赢流水回水记录开始小时";
        numericUpDown_查询输赢流水回水记录开始小时.Size = new Size(43, 23);
        numericUpDown_查询输赢流水回水记录开始小时.TabIndex = 2;
        // 
        // dateTimePicker_查询输赢流水回水记录开始日期
        // 
        dateTimePicker_查询输赢流水回水记录开始日期.Location = new Point(50, 9);
        dateTimePicker_查询输赢流水回水记录开始日期.Name = "dateTimePicker_查询输赢流水回水记录开始日期";
        dateTimePicker_查询输赢流水回水记录开始日期.Size = new Size(123, 23);
        dateTimePicker_查询输赢流水回水记录开始日期.TabIndex = 1;
        // 
        // label14
        // 
        label14.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label14.Location = new Point(4, 8);
        label14.Name = "label14";
        label14.Size = new Size(47, 23);
        label14.TabIndex = 0;
        label14.Text = "开始";
        label14.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // dataGridView_输赢流水回水记录
        // 
        dataGridView_输赢流水回水记录.Dock = DockStyle.Fill;
        dataGridView_输赢流水回水记录.Location = new Point(3, 83);
        dataGridView_输赢流水回水记录.Name = "dataGridView_输赢流水回水记录";
        dataGridView_输赢流水回水记录.Size = new Size(1080, 555);
        dataGridView_输赢流水回水记录.TabIndex = 1;
        // 
        // tabPage_拉手返点
        // 
        tabPage_拉手返点.Controls.Add(tableLayoutPanel3);
        tabPage_拉手返点.Location = new Point(4, 26);
        tabPage_拉手返点.Name = "tabPage_拉手返点";
        tabPage_拉手返点.Padding = new Padding(3);
        tabPage_拉手返点.Size = new Size(1092, 647);
        tabPage_拉手返点.TabIndex = 7;
        tabPage_拉手返点.Text = "拉手返点";
        tabPage_拉手返点.UseVisualStyleBackColor = true;
        // 
        // tableLayoutPanel3
        // 
        tableLayoutPanel3.ColumnCount = 1;
        tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
        tableLayoutPanel3.Controls.Add(panel7, 0, 0);
        tableLayoutPanel3.Controls.Add(dataGridView_拉手返点, 0, 1);
        tableLayoutPanel3.Dock = DockStyle.Fill;
        tableLayoutPanel3.Location = new Point(3, 3);
        tableLayoutPanel3.Name = "tableLayoutPanel3";
        tableLayoutPanel3.RowCount = 2;
        tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));
        tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
        tableLayoutPanel3.Size = new Size(1086, 641);
        tableLayoutPanel3.TabIndex = 4;
        // 
        // panel7
        // 
        panel7.Controls.Add(button_根据条件查询拉手返点);
        panel7.Controls.Add(textBox_拉手名称);
        panel7.Controls.Add(label15);
        panel7.Controls.Add(numericUpDown_查询拉手返点结束分钟);
        panel7.Controls.Add(numericUpDown_查询拉手返点结束小时);
        panel7.Controls.Add(dateTimePicker_查询拉手返点结束日期);
        panel7.Controls.Add(label16);
        panel7.Controls.Add(numericUpDown_查询拉手返点开始分钟);
        panel7.Controls.Add(numericUpDown_查询拉手返点开始小时);
        panel7.Controls.Add(dateTimePicker_查询拉手返点开始日期);
        panel7.Controls.Add(label17);
        panel7.Dock = DockStyle.Fill;
        panel7.Location = new Point(3, 3);
        panel7.Name = "panel7";
        panel7.Size = new Size(1080, 74);
        panel7.TabIndex = 0;
        // 
        // button_根据条件查询拉手返点
        // 
        button_根据条件查询拉手返点.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        button_根据条件查询拉手返点.Location = new Point(460, 7);
        button_根据条件查询拉手返点.Name = "button_根据条件查询拉手返点";
        button_根据条件查询拉手返点.Size = new Size(95, 30);
        button_根据条件查询拉手返点.TabIndex = 13;
        button_根据条件查询拉手返点.Text = "查询统计";
        button_根据条件查询拉手返点.UseVisualStyleBackColor = true;
        // 
        // textBox_拉手名称
        // 
        textBox_拉手名称.BorderStyle = BorderStyle.FixedSingle;
        textBox_拉手名称.Location = new Point(354, 10);
        textBox_拉手名称.Name = "textBox_拉手名称";
        textBox_拉手名称.Size = new Size(100, 23);
        textBox_拉手名称.TabIndex = 9;
        // 
        // label15
        // 
        label15.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label15.Location = new Point(282, 10);
        label15.Name = "label15";
        label15.Size = new Size(69, 23);
        label15.TabIndex = 8;
        label15.Text = "拉手名称";
        label15.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // numericUpDown_查询拉手返点结束分钟
        // 
        numericUpDown_查询拉手返点结束分钟.Location = new Point(227, 41);
        numericUpDown_查询拉手返点结束分钟.Name = "numericUpDown_查询拉手返点结束分钟";
        numericUpDown_查询拉手返点结束分钟.Size = new Size(43, 23);
        numericUpDown_查询拉手返点结束分钟.TabIndex = 7;
        // 
        // numericUpDown_查询拉手返点结束小时
        // 
        numericUpDown_查询拉手返点结束小时.Location = new Point(178, 40);
        numericUpDown_查询拉手返点结束小时.Name = "numericUpDown_查询拉手返点结束小时";
        numericUpDown_查询拉手返点结束小时.Size = new Size(43, 23);
        numericUpDown_查询拉手返点结束小时.TabIndex = 6;
        // 
        // dateTimePicker_查询拉手返点结束日期
        // 
        dateTimePicker_查询拉手返点结束日期.Location = new Point(50, 40);
        dateTimePicker_查询拉手返点结束日期.Name = "dateTimePicker_查询拉手返点结束日期";
        dateTimePicker_查询拉手返点结束日期.Size = new Size(123, 23);
        dateTimePicker_查询拉手返点结束日期.TabIndex = 5;
        // 
        // label16
        // 
        label16.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label16.Location = new Point(4, 39);
        label16.Name = "label16";
        label16.Size = new Size(47, 23);
        label16.TabIndex = 4;
        label16.Text = "结束";
        label16.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // numericUpDown_查询拉手返点开始分钟
        // 
        numericUpDown_查询拉手返点开始分钟.Location = new Point(227, 10);
        numericUpDown_查询拉手返点开始分钟.Name = "numericUpDown_查询拉手返点开始分钟";
        numericUpDown_查询拉手返点开始分钟.Size = new Size(43, 23);
        numericUpDown_查询拉手返点开始分钟.TabIndex = 3;
        // 
        // numericUpDown_查询拉手返点开始小时
        // 
        numericUpDown_查询拉手返点开始小时.Location = new Point(178, 9);
        numericUpDown_查询拉手返点开始小时.Name = "numericUpDown_查询拉手返点开始小时";
        numericUpDown_查询拉手返点开始小时.Size = new Size(43, 23);
        numericUpDown_查询拉手返点开始小时.TabIndex = 2;
        // 
        // dateTimePicker_查询拉手返点开始日期
        // 
        dateTimePicker_查询拉手返点开始日期.Location = new Point(50, 9);
        dateTimePicker_查询拉手返点开始日期.Name = "dateTimePicker_查询拉手返点开始日期";
        dateTimePicker_查询拉手返点开始日期.Size = new Size(123, 23);
        dateTimePicker_查询拉手返点开始日期.TabIndex = 1;
        // 
        // label17
        // 
        label17.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point, 134);
        label17.Location = new Point(4, 8);
        label17.Name = "label17";
        label17.Size = new Size(47, 23);
        label17.TabIndex = 0;
        label17.Text = "开始";
        label17.TextAlign = ContentAlignment.MiddleCenter;
        // 
        // dataGridView_拉手返点
        // 
        dataGridView_拉手返点.Dock = DockStyle.Fill;
        dataGridView_拉手返点.Location = new Point(3, 83);
        dataGridView_拉手返点.Name = "dataGridView_拉手返点";
        dataGridView_拉手返点.Size = new Size(1080, 555);
        dataGridView_拉手返点.TabIndex = 1;
        // 
        // tabPage_全部设置
        // 
        tabPage_全部设置.Controls.Add(panel_Setting);
        tabPage_全部设置.Location = new Point(4, 26);
        tabPage_全部设置.Name = "tabPage_全部设置";
        tabPage_全部设置.Padding = new Padding(3);
        tabPage_全部设置.Size = new Size(1092, 647);
        tabPage_全部设置.TabIndex = 8;
        tabPage_全部设置.Text = "全部设置";
        tabPage_全部设置.UseVisualStyleBackColor = true;
        // 
        // panel_Setting
        // 
        panel_Setting.Controls.Add(groupBox_业务开关);
        panel_Setting.Controls.Add(groupBox_业务参数);
        panel_Setting.Controls.Add(groupBox_系统管理);
        panel_Setting.Dock = DockStyle.Fill;
        panel_Setting.Location = new Point(3, 3);
        panel_Setting.Name = "panel_Setting";
        panel_Setting.Size = new Size(1086, 641);
        panel_Setting.TabIndex = 0;
        // 
        // groupBox_业务开关
        // 
        groupBox_业务开关.Controls.Add(checkBox_发送6路图);
        groupBox_业务开关.Controls.Add(checkBox_发送7路图);
        groupBox_业务开关.Controls.Add(checkBox_开启对冲);
        groupBox_业务开关.Controls.Add(checkBox_假人自动上分);
        groupBox_业务开关.Controls.Add(checkBox_自动回水);
        groupBox_业务开关.Controls.Add(checkBox_开启图片背景);
        groupBox_业务开关.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
        groupBox_业务开关.Location = new Point(30, 30);
        groupBox_业务开关.Name = "groupBox_业务开关";
        groupBox_业务开关.Size = new Size(350, 220);
        groupBox_业务开关.TabIndex = 0;
        groupBox_业务开关.TabStop = false;
        groupBox_业务开关.Text = "业务开关设置";
        // 
        // checkBox_发送6路图
        // 
        checkBox_发送6路图.AutoSize = true;
        checkBox_发送6路图.Font = new Font("Microsoft YaHei UI", 9F);
        checkBox_发送6路图.Location = new Point(20, 30);
        checkBox_发送6路图.Name = "checkBox_发送6路图";
        checkBox_发送6路图.Size = new Size(106, 21);
        checkBox_发送6路图.TabIndex = 0;
        checkBox_发送6路图.Text = "发送6行路子图";
        checkBox_发送6路图.UseVisualStyleBackColor = true;
        checkBox_发送6路图.CheckedChanged += CheckBox_系统设置_CheckedChanged;
        // 
        // checkBox_发送7路图
        // 
        checkBox_发送7路图.AutoSize = true;
        checkBox_发送7路图.Font = new Font("Microsoft YaHei UI", 9F);
        checkBox_发送7路图.Location = new Point(20, 60);
        checkBox_发送7路图.Name = "checkBox_发送7路图";
        checkBox_发送7路图.Size = new Size(106, 21);
        checkBox_发送7路图.TabIndex = 1;
        checkBox_发送7路图.Text = "发送7行路子图";
        checkBox_发送7路图.UseVisualStyleBackColor = true;
        checkBox_发送7路图.CheckedChanged += CheckBox_系统设置_CheckedChanged;
        // 
        // checkBox_开启对冲
        // 
        checkBox_开启对冲.AutoSize = true;
        checkBox_开启对冲.Font = new Font("Microsoft YaHei UI", 9F);
        checkBox_开启对冲.Location = new Point(20, 90);
        checkBox_开启对冲.Name = "checkBox_开启对冲";
        checkBox_开启对冲.Size = new Size(75, 21);
        checkBox_开启对冲.TabIndex = 2;
        checkBox_开启对冲.Text = "对冲吃单";
        checkBox_开启对冲.UseVisualStyleBackColor = true;
        checkBox_开启对冲.CheckedChanged += CheckBox_系统设置_CheckedChanged;
        // 
        // checkBox_假人自动上分
        // 
        checkBox_假人自动上分.AutoSize = true;
        checkBox_假人自动上分.Font = new Font("Microsoft YaHei UI", 9F);
        checkBox_假人自动上分.Location = new Point(20, 120);
        checkBox_假人自动上分.Name = "checkBox_假人自动上分";
        checkBox_假人自动上分.Size = new Size(99, 21);
        checkBox_假人自动上分.TabIndex = 3;
        checkBox_假人自动上分.Text = "假人自动上分";
        checkBox_假人自动上分.UseVisualStyleBackColor = true;
        checkBox_假人自动上分.CheckedChanged += CheckBox_系统设置_CheckedChanged;
        // 
        // checkBox_自动回水
        // 
        checkBox_自动回水.AutoSize = true;
        checkBox_自动回水.Font = new Font("Microsoft YaHei UI", 9F);
        checkBox_自动回水.Location = new Point(20, 150);
        checkBox_自动回水.Name = "checkBox_自动回水";
        checkBox_自动回水.Size = new Size(75, 21);
        checkBox_自动回水.TabIndex = 4;
        checkBox_自动回水.Text = "自动回水";
        checkBox_自动回水.UseVisualStyleBackColor = true;
        checkBox_自动回水.CheckedChanged += CheckBox_系统设置_CheckedChanged;
        // 
        // checkBox_开启图片背景
        // 
        checkBox_开启图片背景.AutoSize = true;
        checkBox_开启图片背景.Font = new Font("Microsoft YaHei UI", 9F);
        checkBox_开启图片背景.Location = new Point(20, 180);
        checkBox_开启图片背景.Name = "checkBox_开启图片背景";
        checkBox_开启图片背景.Size = new Size(99, 21);
        checkBox_开启图片背景.TabIndex = 5;
        checkBox_开启图片背景.Text = "显示图片背景";
        checkBox_开启图片背景.UseVisualStyleBackColor = true;
        checkBox_开启图片背景.CheckedChanged += CheckBox_系统设置_CheckedChanged;
        // 
        // groupBox_业务参数
        // 
        groupBox_业务参数.Controls.Add(label_返点比例);
        groupBox_业务参数.Controls.Add(numericUpDown_返点比例);
        groupBox_业务参数.Controls.Add(label_单期限额);
        groupBox_业务参数.Controls.Add(numericUpDown_单期限额);
        groupBox_业务参数.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
        groupBox_业务参数.Location = new Point(400, 30);
        groupBox_业务参数.Name = "groupBox_业务参数";
        groupBox_业务参数.Size = new Size(350, 220);
        groupBox_业务参数.TabIndex = 1;
        groupBox_业务参数.TabStop = false;
        groupBox_业务参数.Text = "业务参数设置";
        // 
        // label_返点比例
        // 
        label_返点比例.AutoSize = true;
        label_返点比例.Font = new Font("Microsoft YaHei UI", 9F);
        label_返点比例.Location = new Point(20, 35);
        label_返点比例.Name = "label_返点比例";
        label_返点比例.Size = new Size(87, 17);
        label_返点比例.TabIndex = 0;
        label_返点比例.Text = "返点比例(%)：";
        // 
        // numericUpDown_返点比例
        // 
        numericUpDown_返点比例.DecimalPlaces = 1;
        numericUpDown_返点比例.Font = new Font("Microsoft YaHei UI", 9F);
        numericUpDown_返点比例.Increment = new decimal(new int[] { 1, 0, 0, 65536 });
        numericUpDown_返点比例.Location = new Point(110, 32);
        numericUpDown_返点比例.Name = "numericUpDown_返点比例";
        numericUpDown_返点比例.Size = new Size(120, 23);
        numericUpDown_返点比例.TabIndex = 1;
        numericUpDown_返点比例.Value = new decimal(new int[] { 15, 0, 0, 65536 });
        numericUpDown_返点比例.ValueChanged += NumericUpDown_系统设置_ValueChanged;
        // 
        // label_单期限额
        // 
        label_单期限额.AutoSize = true;
        label_单期限额.Font = new Font("Microsoft YaHei UI", 9F);
        label_单期限额.Location = new Point(20, 70);
        label_单期限额.Name = "label_单期限额";
        label_单期限额.Size = new Size(88, 17);
        label_单期限额.TabIndex = 2;
        label_单期限额.Text = "单期限额(元)：";
        // 
        // numericUpDown_单期限额
        // 
        numericUpDown_单期限额.Font = new Font("Microsoft YaHei UI", 9F);
        numericUpDown_单期限额.Increment = new decimal(new int[] { 1000, 0, 0, 0 });
        numericUpDown_单期限额.Location = new Point(110, 67);
        numericUpDown_单期限额.Maximum = new decimal(new int[] { 10000000, 0, 0, 0 });
        numericUpDown_单期限额.Minimum = new decimal(new int[] { 1000, 0, 0, 0 });
        numericUpDown_单期限额.Name = "numericUpDown_单期限额";
        numericUpDown_单期限额.Size = new Size(120, 23);
        numericUpDown_单期限额.TabIndex = 3;
        numericUpDown_单期限额.Value = new decimal(new int[] { 100000, 0, 0, 0 });
        numericUpDown_单期限额.ValueChanged += NumericUpDown_系统设置_ValueChanged;
        // 
        // groupBox_系统管理
        // 
        groupBox_系统管理.Controls.Add(label_系统管理说明);
        groupBox_系统管理.Controls.Add(button_初始化出厂设置);
        groupBox_系统管理.Controls.Add(button_清除一切记录);
        groupBox_系统管理.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold);
        groupBox_系统管理.ForeColor = Color.Red;
        groupBox_系统管理.Location = new Point(30, 270);
        groupBox_系统管理.Name = "groupBox_系统管理";
        groupBox_系统管理.Size = new Size(720, 200);
        groupBox_系统管理.TabIndex = 1;
        groupBox_系统管理.TabStop = false;
        groupBox_系统管理.Text = "系统管理（危险操作）";
        // 
        // label_系统管理说明
        // 
        label_系统管理说明.Font = new Font("Microsoft YaHei UI", 9F);
        label_系统管理说明.ForeColor = Color.DarkRed;
        label_系统管理说明.Location = new Point(30, 110);
        label_系统管理说明.Name = "label_系统管理说明";
        label_系统管理说明.Size = new Size(660, 70);
        label_系统管理说明.TabIndex = 2;
        label_系统管理说明.Text = "⚠️ 危险操作警告：\r\n\r\n🔸 清除一切记录：删除所有投注记录、上下分记录、输赢流水、会员信息（除管理员外）、日志文件，保留系统参数设置。此操作不可恢复！\r\n\r\n🔸 初始化出厂设置：执行\"清除一切记录\"的所有操作，同时重置所有系统参数为出厂默认值，包括返点比例、时间设置等所有配置。此操作不可恢复！\r\n\r\n请在执行前确保已备份重要数据！";
        // 
        // button_初始化出厂设置
        // 
        button_初始化出厂设置.BackColor = Color.Red;
        button_初始化出厂设置.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold);
        button_初始化出厂设置.ForeColor = Color.White;
        button_初始化出厂设置.Location = new Point(250, 40);
        button_初始化出厂设置.Name = "button_初始化出厂设置";
        button_初始化出厂设置.Size = new Size(200, 50);
        button_初始化出厂设置.TabIndex = 1;
        button_初始化出厂设置.Text = "初始化出厂设置";
        button_初始化出厂设置.UseVisualStyleBackColor = false;
        button_初始化出厂设置.Click += button_初始化出厂设置_Click;
        // 
        // button_清除一切记录
        // 
        button_清除一切记录.BackColor = Color.Orange;
        button_清除一切记录.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Bold);
        button_清除一切记录.ForeColor = Color.White;
        button_清除一切记录.Location = new Point(30, 40);
        button_清除一切记录.Name = "button_清除一切记录";
        button_清除一切记录.Size = new Size(200, 50);
        button_清除一切记录.TabIndex = 0;
        button_清除一切记录.Text = "清除一切记录";
        button_清除一切记录.UseVisualStyleBackColor = false;
        button_清除一切记录.Click += button_清除一切记录_Click;
        // 
        // contextMenuStrip_Members
        // 
        contextMenuStrip_Members.Items.AddRange(new ToolStripItem[] { toolStripMenuItem_RefreshData, toolStripMenuItem_MemberAccount, toolStripMenuItem_MemberNickName, toolStripMenuItem_MemberRemark, toolStripMenuItem_MemberTotalDeposit, toolStripMenuItem_MemberTotalWithdraw, toolStripSeparator1, toolStripMenuItem_OperateDeposit, toolStripMenuItem_OperateWithdraw, toolStripMenuItem_ModifyRebateRate, toolStripMenuItem_ModifyRemark, toolStripMenuItem_SetAgentParent, toolStripMenuItem_ToggleUserType });
        contextMenuStrip_Members.Name = "contextMenuStrip_Members";
        contextMenuStrip_Members.Size = new Size(149, 274);
        // 
        // toolStripMenuItem_RefreshData
        // 
        toolStripMenuItem_RefreshData.Name = "toolStripMenuItem_RefreshData";
        toolStripMenuItem_RefreshData.Size = new Size(148, 22);
        toolStripMenuItem_RefreshData.Text = "刷新数据";
        // 
        // toolStripMenuItem_MemberAccount
        // 
        toolStripMenuItem_MemberAccount.Enabled = false;
        toolStripMenuItem_MemberAccount.Name = "toolStripMenuItem_MemberAccount";
        toolStripMenuItem_MemberAccount.Size = new Size(148, 22);
        toolStripMenuItem_MemberAccount.Text = "账号：";
        // 
        // toolStripMenuItem_MemberNickName
        // 
        toolStripMenuItem_MemberNickName.Enabled = false;
        toolStripMenuItem_MemberNickName.Name = "toolStripMenuItem_MemberNickName";
        toolStripMenuItem_MemberNickName.Size = new Size(148, 22);
        toolStripMenuItem_MemberNickName.Text = "昵称：";
        // 
        // toolStripMenuItem_MemberRemark
        // 
        toolStripMenuItem_MemberRemark.Enabled = false;
        toolStripMenuItem_MemberRemark.Name = "toolStripMenuItem_MemberRemark";
        toolStripMenuItem_MemberRemark.Size = new Size(148, 22);
        toolStripMenuItem_MemberRemark.Text = "备注名：";
        // 
        // toolStripMenuItem_MemberTotalDeposit
        // 
        toolStripMenuItem_MemberTotalDeposit.Enabled = false;
        toolStripMenuItem_MemberTotalDeposit.Name = "toolStripMenuItem_MemberTotalDeposit";
        toolStripMenuItem_MemberTotalDeposit.Size = new Size(148, 22);
        toolStripMenuItem_MemberTotalDeposit.Text = "总上分：";
        // 
        // toolStripMenuItem_MemberTotalWithdraw
        // 
        toolStripMenuItem_MemberTotalWithdraw.Enabled = false;
        toolStripMenuItem_MemberTotalWithdraw.Name = "toolStripMenuItem_MemberTotalWithdraw";
        toolStripMenuItem_MemberTotalWithdraw.Size = new Size(148, 22);
        toolStripMenuItem_MemberTotalWithdraw.Text = "总下分：";
        // 
        // toolStripSeparator1
        // 
        toolStripSeparator1.Name = "toolStripSeparator1";
        toolStripSeparator1.Size = new Size(145, 6);
        // 
        // toolStripMenuItem_OperateDeposit
        // 
        toolStripMenuItem_OperateDeposit.Name = "toolStripMenuItem_OperateDeposit";
        toolStripMenuItem_OperateDeposit.Size = new Size(148, 22);
        toolStripMenuItem_OperateDeposit.Text = "操作上分";
        // 
        // toolStripMenuItem_OperateWithdraw
        // 
        toolStripMenuItem_OperateWithdraw.Name = "toolStripMenuItem_OperateWithdraw";
        toolStripMenuItem_OperateWithdraw.Size = new Size(148, 22);
        toolStripMenuItem_OperateWithdraw.Text = "操作下分";
        // 
        // toolStripMenuItem_ModifyRebateRate
        // 
        toolStripMenuItem_ModifyRebateRate.Name = "toolStripMenuItem_ModifyRebateRate";
        toolStripMenuItem_ModifyRebateRate.Size = new Size(148, 22);
        toolStripMenuItem_ModifyRebateRate.Text = "修改回水比例";
        // 
        // toolStripMenuItem_ModifyRemark
        // 
        toolStripMenuItem_ModifyRemark.Name = "toolStripMenuItem_ModifyRemark";
        toolStripMenuItem_ModifyRemark.Size = new Size(148, 22);
        toolStripMenuItem_ModifyRemark.Text = "修改备注名";
        // 
        // toolStripMenuItem_SetAgentParent
        // 
        toolStripMenuItem_SetAgentParent.Name = "toolStripMenuItem_SetAgentParent";
        toolStripMenuItem_SetAgentParent.Size = new Size(148, 22);
        toolStripMenuItem_SetAgentParent.Text = "设置拉手上级";
        // 
        // toolStripMenuItem_ToggleUserType
        // 
        toolStripMenuItem_ToggleUserType.Name = "toolStripMenuItem_ToggleUserType";
        toolStripMenuItem_ToggleUserType.Size = new Size(148, 22);
        toolStripMenuItem_ToggleUserType.Text = "设为真人";
        // 
        // FormMain
        // 
        AutoScaleDimensions = new SizeF(7F, 17F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(1100, 700);
        Controls.Add(tabControl_Menu);
        Controls.Add(statusStrip1);
        Icon = (Icon)resources.GetObject("$this.Icon");
        Name = "FormMain";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "指令卫士";
        Load += FormMain_Load;
        statusStrip1.ResumeLayout(false);
        statusStrip1.PerformLayout();
        tabControl_Menu.ResumeLayout(false);
        tabPage_主界面.ResumeLayout(false);
        tableLayoutPanel_Body.ResumeLayout(false);
        tableLayoutPanel_Left.ResumeLayout(false);
        panel4.ResumeLayout(false);
        panel2.ResumeLayout(false);
        panel1.ResumeLayout(false);
        ((System.ComponentModel.ISupportInitialize)dgvMembers).EndInit();
        tableLayoutPanel_Right.ResumeLayout(false);
        ((System.ComponentModel.ISupportInitialize)dgvDepositRequests).EndInit();
        ((System.ComponentModel.ISupportInitialize)dgvWithdrawRequests).EndInit();
        ((System.ComponentModel.ISupportInitialize)dgvCurrentIssueBets).EndInit();
        tabPage_指令限额.ResumeLayout(false);
        ((System.ComponentModel.ISupportInitialize)dataGridView_指令限额).EndInit();
        tabPage_投注记录.ResumeLayout(false);
        tableLayoutPanel_投注记录.ResumeLayout(false);
        panel3.ResumeLayout(false);
        panel3.PerformLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询投注记录结束分钟).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询投注记录结束小时).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询投注记录开始分钟).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询投注记录开始小时).EndInit();
        ((System.ComponentModel.ISupportInitialize)dataGridView_投注记录).EndInit();
        tabPage_上下分记录.ResumeLayout(false);
        tableLayoutPanel1.ResumeLayout(false);
        panel5.ResumeLayout(false);
        panel5.PerformLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询上下分记录结束分钟).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询上下分记录结束小时).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询上下分记录开始分钟).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询上下分记录开始小时).EndInit();
        ((System.ComponentModel.ISupportInitialize)dataGridView_上下分记录).EndInit();
        tabPage_输赢流水回水记录.ResumeLayout(false);
        tableLayoutPanel2.ResumeLayout(false);
        panel6.ResumeLayout(false);
        panel6.PerformLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询输赢流水回水记录结束分钟).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询输赢流水回水记录结束小时).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询输赢流水回水记录开始分钟).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询输赢流水回水记录开始小时).EndInit();
        ((System.ComponentModel.ISupportInitialize)dataGridView_输赢流水回水记录).EndInit();
        tabPage_拉手返点.ResumeLayout(false);
        tableLayoutPanel3.ResumeLayout(false);
        panel7.ResumeLayout(false);
        panel7.PerformLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询拉手返点结束分钟).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询拉手返点结束小时).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询拉手返点开始分钟).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_查询拉手返点开始小时).EndInit();
        ((System.ComponentModel.ISupportInitialize)dataGridView_拉手返点).EndInit();
        tabPage_全部设置.ResumeLayout(false);
        panel_Setting.ResumeLayout(false);
        groupBox_业务开关.ResumeLayout(false);
        groupBox_业务开关.PerformLayout();
        groupBox_业务参数.ResumeLayout(false);
        groupBox_业务参数.PerformLayout();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_返点比例).EndInit();
        ((System.ComponentModel.ISupportInitialize)numericUpDown_单期限额).EndInit();
        groupBox_系统管理.ResumeLayout(false);
        contextMenuStrip_Members.ResumeLayout(false);
        ResumeLayout(false);
        PerformLayout();
    }

    private System.Windows.Forms.CheckBox checkBox_OneKeyRebate;
    private System.Windows.Forms.Button button_OneKeyRebate;

    private System.Windows.Forms.DataGridView dgvCurrentIssueBets;

    private System.Windows.Forms.DataGridView dgvMembers;
    private System.Windows.Forms.ContextMenuStrip contextMenuStrip_Members;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_RefreshData;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_MemberAccount;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_MemberNickName;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_MemberRemark;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_MemberTotalDeposit;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_MemberTotalWithdraw;
    private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_OperateDeposit;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_OperateWithdraw;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_ModifyRebateRate;

    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_ModifyRemark;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_ToggleUserType;
    private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem_SetAgentParent;
    private System.Windows.Forms.DataGridView dgvDepositRequests;
    private System.Windows.Forms.DataGridView dgvWithdrawRequests;

    private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Right;

    private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Left;

    private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Body;

    private System.Windows.Forms.TabControl tabControl_Menu;
    private System.Windows.Forms.TabPage tabPage_主界面;

    private System.Windows.Forms.StatusStrip statusStrip1;
    private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
    private System.Windows.Forms.ToolStripSeparator toolStripStatusSeparator1;
    private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel2;
    private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel3;
    private System.Windows.Forms.ToolStripSeparator toolStripStatusSeparator2;
    private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel4;
    private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel5;
    private System.Windows.Forms.ToolStripSeparator toolStripStatusSeparator3;
    private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel6;
    private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel7;

    #endregion

    private System.Windows.Forms.Panel panel1;
    private ComboBox comboBox_WorkGroupId;
    private Button button_卡奖时手动开奖或退单;
    private Button button_选择Q群;
    private System.Windows.Forms.Panel panel4;
    private System.Windows.Forms.Button button_StopBet;
    private System.Windows.Forms.Button button_StartBet;
    private System.Windows.Forms.Button button_StopService;
    private System.Windows.Forms.Button button_StartService;
    private System.Windows.Forms.Panel panel2;
    private Label label_总积分;
    private Label label_总人数;
    private Button button_撤销用户投注;
    private Button button_开奖历史;
    private Label label_番摊结果;
    private Label label10;
    private Label label_开奖号码;
    private Label label8;
    private Label label_收单状态;
    private Label label_开奖期数;
    private Label label_开奖期数标题;
    private Label label_封盘倒计时;
    private Label label_正在投注期数;
    private Label label_正在投注期数标题;
    private System.Windows.Forms.TabPage tabPage_投注记录;
    private TableLayoutPanel tableLayoutPanel_投注记录;
    private Panel panel3;
    private Label label_显示查询投注记录有效下注总额以及总盈亏;
    private Button button_根据选择条件查询投注记录;
    private CheckBox checkBox_查询投注记录包含假人;
    private TextBox textBox_根据账号查询投注记录;
    private Label label3;
    private TextBox textBox_根据期号查询投注记录;
    private Label label2;
    private NumericUpDown numericUpDown_查询投注记录结束分钟;
    private NumericUpDown numericUpDown_查询投注记录结束小时;
    private DateTimePicker dateTimePicker_查询投注记录结束日期;
    private Label label1;
    private NumericUpDown numericUpDown_查询投注记录开始分钟;
    private NumericUpDown numericUpDown_查询投注记录开始小时;
    private DateTimePicker dateTimePicker_查询投注记录开始日期;
    private Label label_查询投注记录开始时间;
    private DataGridView dataGridView_投注记录;
    private TabPage tabPage_上下分记录;
    private TableLayoutPanel tableLayoutPanel1;
    private Panel panel5;
    private Label label_显示查询上下分统计数据;
    private Button button_根据条件查询上下分记录;
    private CheckBox checkBox_查询上下分记录包含假人;
    private TextBox textBox_查询上下分记录账号;
    private Label label5;
    private Label label6;
    private NumericUpDown numericUpDown_查询上下分记录结束分钟;
    private NumericUpDown numericUpDown_查询上下分记录结束小时;
    private DateTimePicker dateTimePicker_查询上下分记录结束日期;
    private Label label7;
    private NumericUpDown numericUpDown_查询上下分记录开始分钟;
    private NumericUpDown numericUpDown_查询上下分记录开始小时;
    private DateTimePicker dateTimePicker_查询上下分记录开始日期;
    private Label label9;
    private DataGridView dataGridView_上下分记录;
    private TabPage tabPage_输赢流水回水记录;
    private TableLayoutPanel tableLayoutPanel2;
    private System.Windows.Forms.Panel panel6;
    private Button button_根据条件查询输赢流水回水记录;
    private CheckBox checkBox_查询输赢流水回水记录包含假人;
    private TextBox textBox_查询输赢流水回水记录账号;
    private Label label11;
    private TextBox textBox_查询输赢流水回水记录期号;
    private Label label12;
    private NumericUpDown numericUpDown_查询输赢流水回水记录结束分钟;
    private NumericUpDown numericUpDown_查询输赢流水回水记录结束小时;
    private DateTimePicker dateTimePicker_查询输赢流水回水记录结束日期;
    private Label label13;
    private NumericUpDown numericUpDown_查询输赢流水回水记录开始分钟;
    private NumericUpDown numericUpDown_查询输赢流水回水记录开始小时;
    private DateTimePicker dateTimePicker_查询输赢流水回水记录开始日期;
    private Label label14;
    private DataGridView dataGridView_输赢流水回水记录;
    private TabPage tabPage_拉手返点;
    private TableLayoutPanel tableLayoutPanel3;
    private Panel panel7;
    private Button button_根据条件查询拉手返点;
    private TextBox textBox_拉手名称;
    private Label label15;
    private NumericUpDown numericUpDown_查询拉手返点结束分钟;
    private NumericUpDown numericUpDown_查询拉手返点结束小时;
    private DateTimePicker dateTimePicker_查询拉手返点结束日期;
    private Label label16;
    private NumericUpDown numericUpDown_查询拉手返点开始分钟;
    private NumericUpDown numericUpDown_查询拉手返点开始小时;
    private DateTimePicker dateTimePicker_查询拉手返点开始日期;
    private Label label17;
    private DataGridView dataGridView_拉手返点;
    private TabPage tabPage_全部设置;
    private Panel panel_Setting;
    private GroupBox groupBox_系统管理;
    private Button button_清除一切记录;
    private Button button_初始化出厂设置;


    private GroupBox groupBox_业务开关;
    private CheckBox checkBox_发送6路图;
    private CheckBox checkBox_发送7路图;
    private CheckBox checkBox_开启对冲;
    private CheckBox checkBox_假人自动上分;
    private CheckBox checkBox_自动回水;
    private CheckBox checkBox_开启图片背景;
    private GroupBox groupBox_业务参数;
    private Label label_返点比例;
    private NumericUpDown numericUpDown_返点比例;
    private Label label_单期限额;
    private NumericUpDown numericUpDown_单期限额;

    private Label label_系统管理说明;
    private ComboBox comboBox__查询上下分记录类型;
    private ToolStripStatusLabel toolStripStatusLabel8;
    private ToolStripStatusLabel toolStripStatusLabel9;
    private ToolStripStatusLabel toolStripStatusLabel10;
    private ToolStripStatusLabel toolStripStatusLabel11;
    private TabPage tabPage_指令限额;
    private DataGridView dataGridView_指令限额;
}
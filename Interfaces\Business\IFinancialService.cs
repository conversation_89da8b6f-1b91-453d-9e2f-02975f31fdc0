using CommandGuard.Enums;
using CommandGuard.Models;

namespace CommandGuard.Interfaces.Business;

/// <summary>
/// 财务服务接口
/// 处理余额变更和财务记录
/// </summary>
public interface IFinancialService
{
    #region 余额操作

    /// <summary>
    /// 增加用户余额
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="amount">增加金额</param>
    /// <param name="referenceType">关联类型</param>
    /// <param name="referenceId">关联ID</param>
    /// <param name="description">操作描述</param>
    /// <param name="operator">操作人员</param>
    /// <param name="note">备注</param>
    /// <returns>是否操作成功</returns>
    Task<bool> IncreaseBalanceAsync(string account, decimal amount, string referenceType, long referenceId,
        string description, string @operator, string note = "");

    /// <summary>
    /// 减少用户余额
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="amount">减少金额</param>
    /// <param name="referenceType">关联类型</param>
    /// <param name="referenceId">关联ID</param>
    /// <param name="description">操作描述</param>
    /// <param name="operator">操作人员</param>
    /// <param name="note">备注</param>
    /// <returns>是否操作成功</returns>
    Task<bool> DecreaseBalanceAsync(string account, decimal amount, string referenceType, long referenceId,
        string description, string @operator, string note = "");

    /// <summary>
    /// 检查用户余额是否足够
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="amount">需要的金额</param>
    /// <returns>是否足够</returns>
    Task<bool> CheckBalanceSufficientAsync(string account, decimal amount);

    /// <summary>
    /// 获取用户当前余额
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>当前余额</returns>
    Task<decimal> GetCurrentBalanceAsync(string account);

    #endregion

    #region 财务记录

    /// <summary>
    /// 创建财务记录
    /// </summary>
    /// <param name="record">财务记录</param>
    /// <returns>记录ID</returns>
    Task<long> CreateFinancialRecordAsync(FinancialRecord record);

    /// <summary>
    /// 获取用户财务记录
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="type">交易类型（可选）</param>
    /// <returns>财务记录列表</returns>
    Task<List<FinancialRecord>> GetFinancialRecordsAsync(string account, DateTime? startTime = null,
        DateTime? endTime = null, EnumFinancialType? type = null);

    /// <summary>
    /// 获取财务统计信息
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>统计信息</returns>
    Task<FinancialStatistics> GetFinancialStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null);

    #endregion

    #region 事务操作

    /// <summary>
    /// 执行上分操作（增加余额并记录）
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="amount">上分金额</param>
    /// <param name="depositRequestId">上分申请ID</param>
    /// <param name="operator">操作人员</param>
    /// <param name="note">备注</param>
    /// <returns>是否操作成功</returns>
    Task<bool> ProcessDepositAsync(string account, decimal amount, long depositRequestId, string @operator, string note = "");

    /// <summary>
    /// 执行下分操作（减少余额并记录）
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="amount">下分金额</param>
    /// <param name="withdrawRequestId">下分申请ID</param>
    /// <param name="operator">操作人员</param>
    /// <param name="note">备注</param>
    /// <returns>是否操作成功</returns>
    Task<bool> ProcessWithdrawAsync(string account, decimal amount, long withdrawRequestId, string @operator, string note = "");

    #endregion
}

/// <summary>
/// 财务统计信息
/// </summary>
public class FinancialStatistics
{
    /// <summary>
    /// 总上分金额
    /// </summary>
    public decimal TotalDepositAmount { get; set; }

    /// <summary>
    /// 总下分金额
    /// </summary>
    public decimal TotalWithdrawAmount { get; set; }

    /// <summary>
    /// 净流入金额（上分-下分）
    /// </summary>
    public decimal NetInflowAmount { get; set; }

    /// <summary>
    /// 交易笔数
    /// </summary>
    public int TransactionCount { get; set; }

    /// <summary>
    /// 涉及用户数
    /// </summary>
    public int UserCount { get; set; }
}
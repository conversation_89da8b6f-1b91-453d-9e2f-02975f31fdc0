{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Warning", "System": "Warning", "CommandGuard.Services.Business.BetRecordService": "Warning", "CommandGuard.Services.Business.CommandService": "Information", "CommandGuard.Services.Business.DepositWithdrawRecordService": "Warning", "CommandGuard.Services.Lottery.IssueTimeService": "Warning", "CommandGuard.Services.Business.RobotService": "Information", "CommandGuard.Forms.FormMain": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}", "restrictedToMinimumLevel": "Warning"}}, {"Name": "File", "Args": {"path": "logs/app-.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "shared": true}}, {"Name": "File", "Args": {"path": "logs/error-.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}", "restrictedToMinimumLevel": "Error", "retainedFileCountLimit": 90, "fileSizeLimitBytes": 52428800, "rollOnFileSizeLimit": true, "shared": true}}]}}
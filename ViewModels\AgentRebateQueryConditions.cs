namespace CommandGuard.ViewModels;

/// <summary>
/// 拉手返点查询条件
/// 封装拉手返点查询界面的所有查询参数
/// </summary>
public class AgentRebateQueryConditions
{
    /// <summary>
    /// 开始时间
    /// 查询的起始时间点
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// 查询的结束时间点
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 拉手名称
    /// 可选的拉手名称过滤条件，为null或空字符串表示不按拉手名称过滤
    /// </summary>
    public string? AgentName { get; set; }

    /// <summary>
    /// 查询时间跨度
    /// 计算开始时间和结束时间之间的时间差
    /// </summary>
    public TimeSpan TimeSpan => EndTime - StartTime;

    /// <summary>
    /// 验证查询条件的有效性
    /// </summary>
    /// <returns>验证结果和错误信息</returns>
    public (bool IsValid, string ErrorMessage) Validate()
    {
        if (StartTime >= EndTime)
        {
            return (false, @"开始时间必须小于结束时间");
        }

        if (TimeSpan.TotalDays > 365)
        {
            return (false, @"查询时间跨度不能超过365天");
        }

        // 允许查询未来7天内的时间，这样可以预设查询条件
        if (EndTime.Date > DateTime.Today.AddDays(7))
        {
            return (false, @"结束时间不能超过未来7天");
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 获取查询条件的描述信息
    /// </summary>
    /// <returns>查询条件描述</returns>
    public string GetDescription()
    {
        var agentFilter = string.IsNullOrWhiteSpace(AgentName) ? @"全部拉手" : $@"拉手: {AgentName}";
        return $@"时间: {StartTime:yyyy-MM-dd HH:mm} ~ {EndTime:yyyy-MM-dd HH:mm}, {agentFilter}";
    }
}

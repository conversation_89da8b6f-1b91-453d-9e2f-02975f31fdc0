using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Lottery;

/// <summary>
/// 赔率服务实现
/// 提供快速的赔率数据查询功能
/// </summary>
public sealed class OddsService(IFreeSql fSql, ILogger<OddsService> logger) : IOddsService
{
    /// <summary>
    /// 初始化默认赔率配置 - 系统首次启动时的赔率设置
    ///
    /// 功能：
    /// - 检查数据库中是否已有赔率配置
    /// - 如果没有则创建默认的赔率配置
    /// - 包含所有支持的投注项目和对应赔率
    ///
    /// 默认赔率项目：
    /// - 大小、单双、豹子、对子等
    /// - 各种号码组合投注
    /// - 三门投注等特殊玩法
    ///
    /// 使用场景：
    /// - 系统初始化
    /// - 出厂设置恢复
    /// - 数据库重置后的配置恢复
    /// </summary>
    public async Task<bool> InitializeDefaultOddsAsync()
    {
        try
        {
            var existingCount = await fSql.Select<OddsConfig>().CountAsync();
            if (existingCount > 0) return true;

            var defaultConfigs = CreateDefaultOddsConfigs();
            var result = await fSql.Insert<OddsConfig>()
                .AppendData(defaultConfigs)
                .ExecuteAffrowsAsync();

            logger.LogInformation(@"创建默认赔率配置: {Count} 条", result);
            return result > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"初始化默认赔率配置失败");
            return false;
        }
    }

    /// <summary>
    /// 强制重置赔率配置为出厂默认值 - 出厂设置专用
    ///
    /// 功能：
    /// - 强制清除所有现有赔率配置
    /// - 重新创建默认赔率配置
    /// - 不检查现有配置，直接重置
    ///
    /// 与 InitializeDefaultOddsAsync 的区别：
    /// - InitializeDefaultOddsAsync: 只在没有配置时创建
    /// - ResetToFactoryDefaultOddsAsync: 强制清除并重新创建
    ///
    /// 使用场景：
    /// - 出厂设置重置
    /// - 强制恢复默认配置
    /// </summary>
    public async Task<bool> ResetToFactoryDefaultOddsAsync()
    {
        try
        {
            logger.LogInformation(@"开始强制重置赔率配置为出厂默认值");

            // 1. 强制清除所有现有赔率配置
            var deletedCount = await fSql.Delete<OddsConfig>().Where("1=1").ExecuteAffrowsAsync();
            logger.LogInformation(@"已清除现有赔率配置: {Count} 条", deletedCount);

            // 2. 创建默认赔率配置
            var defaultConfigs = CreateDefaultOddsConfigs();
            var insertedCount = await fSql.Insert<OddsConfig>()
                .AppendData(defaultConfigs)
                .ExecuteAffrowsAsync();

            logger.LogInformation(@"已创建默认赔率配置: {Count} 条", insertedCount);

            var success = insertedCount > 0;
            if (success)
            {
                logger.LogInformation(@"强制重置赔率配置为出厂默认值成功");
            }
            else
            {
                logger.LogWarning(@"强制重置赔率配置为出厂默认值失败：未能创建默认配置");
            }

            return success;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"强制重置赔率配置为出厂默认值失败");
            return false;
        }
    }

    /// <summary>
    /// 获取所有赔率配置
    /// </summary>
    public async Task<List<OddsConfig>> GetAllOddsAsync()
    {
        try
        {
            return await fSql.Select<OddsConfig>()
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.PlayItem)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取所有赔率配置失败");
            return [];
        }
    }

    /// <summary>
    /// 根据投注项目获取赔率配置 - 投注时的赔率查询
    ///
    /// 功能：
    /// - 根据投注项目名称查询对应的赔率
    /// - 用于投注时计算可能的中奖金额
    /// - 支持所有已配置的投注项目
    ///
    /// 投注项目示例：
    /// - "大"、"小"、"单"、"双"
    /// - "豹子"、"对子"、"顺子"
    /// - "三门123"、"三门124"等
    ///
    /// 返回值：找到返回赔率配置，未找到返回null
    /// </summary>
    public async Task<OddsConfig?> GetOddsByPlayItemAsync(string playItem)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(playItem)) return null;

            return await fSql.Select<OddsConfig>()
                .Where(x => x.PlayItem == playItem.Trim())
                .FirstAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取赔率配置失败: {PlayItem}", playItem);
            return null;
        }
    }

    /// <summary>
    /// 创建赔率配置
    /// </summary>
    public async Task<bool> CreateOddsAsync(OddsConfig oddsConfig)
    {
        try
        {
            var result = await fSql.Insert<OddsConfig>().AppendData(oddsConfig).ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"创建赔率配置失败: {PlayItem}", oddsConfig.PlayItem);
            return false;
        }
    }

    /// <summary>
    /// 更新赔率配置
    /// </summary>
    public async Task<bool> UpdateOddsAsync(OddsConfig oddsConfig)
    {
        try
        {
            var result = await fSql.Update<OddsConfig>().SetSource(oddsConfig).ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"更新赔率配置失败: {PlayItem}", oddsConfig.PlayItem);
            return false;
        }
    }

    /// <summary>
    /// 删除赔率配置
    /// </summary>
    public async Task<bool> DeleteOddsAsync(int id)
    {
        try
        {
            var result = await fSql.Delete<OddsConfig>().Where(x => x.Id == id).ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"删除赔率配置失败: ID={Id}", id);
            return false;
        }
    }

    /// <summary>
    /// 批量更新赔率配置 - 管理员批量调整赔率
    ///
    /// 功能：
    /// - 一次性更新多个投注项目的赔率
    /// - 支持事务操作，确保数据一致性
    /// - 提高批量操作的性能
    ///
    /// 使用场景：
    /// - 管理员批量调整赔率
    /// - 系统维护时的赔率更新
    /// - 特殊活动期间的赔率调整
    ///
    /// 注意：所有更新在同一事务中执行
    /// </summary>
    public async Task<bool> BatchUpdateOddsAsync(List<OddsConfig> oddsConfigs)
    {
        try
        {
            var result = await fSql.Update<OddsConfig>().SetSource(oddsConfigs).ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"批量更新赔率配置失败");
            return false;
        }
    }

    /// <summary>
    /// 检查赔率配置是否存在
    /// </summary>
    public async Task<bool> ExistsAsync(string playItem)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(playItem)) return false;
            var count = await fSql.Select<OddsConfig>().Where(x => x.PlayItem == playItem.Trim()).CountAsync();
            return count > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"检查赔率配置是否存在失败: {PlayItem}", playItem);
            return false;
        }
    }

    /// <summary>
    /// 获取赔率配置数量
    /// </summary>
    public async Task<int> GetCountAsync()
    {
        try
        {
            return (int)await fSql.Select<OddsConfig>().CountAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取赔率配置数量失败");
            return 0;
        }
    }

    /// <summary>
    /// 获取支持的投注项目列表
    /// </summary>
    public async Task<List<string>> GetSupportedPlayItemsAsync()
    {
        try
        {
            var oddsConfigs = await GetAllOddsAsync();
            return oddsConfigs.Select(x => x.PlayItem).ToList();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取支持的投注项目列表失败");
            return [];
        }
    }

    /// <summary>
    /// 创建默认赔率配置
    /// </summary>
    private static List<OddsConfig> CreateDefaultOddsConfigs()
    {
        return
        [
            // 正码 (4个)
            new() { PlayItem = @"1正", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 1 },
            new() { PlayItem = @"2正", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 2 },
            new() { PlayItem = @"3正", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 3 },
            new() { PlayItem = @"4正", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 4 },

            // 番码 (4个)
            new() { PlayItem = @"1番", Odds = 3.835m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 5 },
            new() { PlayItem = @"2番", Odds = 3.835m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 6 },
            new() { PlayItem = @"3番", Odds = 3.835m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 7 },
            new() { PlayItem = @"4番", Odds = 3.835m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 8 },

            // 角码 (4个)
            new() { PlayItem = @"12角", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 9 },
            new() { PlayItem = @"23角", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 10 },
            new() { PlayItem = @"34角", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 11 },
            new() { PlayItem = @"14角", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 12 },

            // 念码 (12个)
            new() { PlayItem = @"1念2", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 13 },
            new() { PlayItem = @"1念3", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 14 },
            new() { PlayItem = @"1念4", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 15 },
            new() { PlayItem = @"2念1", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 16 },
            new() { PlayItem = @"2念3", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 17 },
            new() { PlayItem = @"2念4", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 18 },
            new() { PlayItem = @"3念1", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 19 },
            new() { PlayItem = @"3念2", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 20 },
            new() { PlayItem = @"3念4", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 21 },
            new() { PlayItem = @"4念1", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 22 },
            new() { PlayItem = @"4念2", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 23 },
            new() { PlayItem = @"4念3", Odds = 2.89m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 30000.0m, SortOrder = 24 },

            // 单双 (2个) - 为了测试TotalStake功能，设置较小的总限额
            new() { PlayItem = @"单", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 1000.0m, SortOrder = 25 },
            new() { PlayItem = @"双", Odds = 1.945m, MinStake = 1.0m, MaxStake = 30000.0m, TotalStake = 1000.0m, SortOrder = 26 }
        ];
    }
}
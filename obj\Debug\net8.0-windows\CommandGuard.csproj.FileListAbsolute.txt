F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.exe
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.deps.json
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.runtimeconfig.json
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.pdb
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-arm64\native\WebView2Loader.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x64\native\WebView2Loader.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x86\native\WebView2Loader.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.Management.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\AiHelper.dll.config
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.AssemblyReference.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Forms.FormMain.resources
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Forms.FormStartup.resources
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Properties.Resources.resources
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.GenerateResource.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.GeneratedMSBuildEditorConfig.editorconfig
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.AssemblyInfoInputs.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.AssemblyInfo.cs
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.CoreCompileInputs.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.Fody.CopyLocal.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.Fody.RuntimeCopyLocal.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandG.D8D6B833.Up2Date
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.dll
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\refint\CommandGuard.dll
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.pdb
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.genruntimeconfig.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\ref\CommandGuard.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.exe
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.deps.json
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.runtimeconfig.json
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.pdb
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-arm64\native\WebView2Loader.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x64\native\WebView2Loader.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x86\native\WebView2Loader.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.Management.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\AiHelper.dll.config
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.AssemblyReference.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Forms.FormMain.resources
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Forms.FormStartup.resources
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Properties.Resources.resources
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.GenerateResource.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.GeneratedMSBuildEditorConfig.editorconfig
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.AssemblyInfoInputs.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.AssemblyInfo.cs
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.CoreCompileInputs.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.Fody.CopyLocal.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.Fody.RuntimeCopyLocal.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandG.D8D6B833.Up2Date
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\refint\CommandGuard.dll
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.pdb
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.genruntimeconfig.cache
F:\RobotCopy\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\ref\CommandGuard.dll

namespace CommandGuard.Forms
{
    partial class FormStartup
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormStartup));
            tableLayoutPanel_Main = new TableLayoutPanel();
            panel_Header = new Panel();
            label_Title = new Label();
            pictureBox_Logo = new PictureBox();
            panel_Content = new Panel();
            groupBox_PlatformSelection = new GroupBox();
            comboBox_Platform = new ComboBox();
            panel_Footer = new Panel();
            button_Cancel = new Button();
            button_Confirm = new Button();
            tableLayoutPanel_Main.SuspendLayout();
            panel_Header.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox_Logo).BeginInit();
            panel_Content.SuspendLayout();
            groupBox_PlatformSelection.SuspendLayout();
            panel_Footer.SuspendLayout();
            SuspendLayout();
            // 
            // tableLayoutPanel_Main
            // 
            tableLayoutPanel_Main.ColumnCount = 1;
            tableLayoutPanel_Main.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Main.Controls.Add(panel_Header, 0, 0);
            tableLayoutPanel_Main.Controls.Add(panel_Content, 0, 1);
            tableLayoutPanel_Main.Controls.Add(panel_Footer, 0, 2);
            tableLayoutPanel_Main.Dock = DockStyle.Fill;
            tableLayoutPanel_Main.Location = new Point(0, 0);
            tableLayoutPanel_Main.Name = "tableLayoutPanel_Main";
            tableLayoutPanel_Main.RowCount = 3;
            tableLayoutPanel_Main.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));
            tableLayoutPanel_Main.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel_Main.RowStyles.Add(new RowStyle(SizeType.Absolute, 70F));
            tableLayoutPanel_Main.Size = new Size(520, 320);
            tableLayoutPanel_Main.TabIndex = 0;
            // 
            // panel_Header
            // 
            panel_Header.BackColor = Color.FromArgb(52, 144, 220);
            panel_Header.Controls.Add(label_Title);
            panel_Header.Controls.Add(pictureBox_Logo);
            panel_Header.Dock = DockStyle.Fill;
            panel_Header.Location = new Point(0, 0);
            panel_Header.Margin = new Padding(0);
            panel_Header.Name = "panel_Header";
            panel_Header.Size = new Size(520, 100);
            panel_Header.TabIndex = 0;
            // 
            // label_Title
            // 
            label_Title.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            label_Title.Font = new Font("微软雅黑", 16F, FontStyle.Bold);
            label_Title.ForeColor = Color.White;
            label_Title.Location = new Point(90, 15);
            label_Title.Name = "label_Title";
            label_Title.Size = new Size(420, 70);
            label_Title.TabIndex = 1;
            label_Title.Text = "聊天平台配置\r\n请选择要使用的聊天平台";
            label_Title.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // pictureBox_Logo
            // 
            pictureBox_Logo.Anchor = AnchorStyles.Left;
            pictureBox_Logo.BackColor = Color.White;
            pictureBox_Logo.Location = new Point(25, 25);
            pictureBox_Logo.Name = "pictureBox_Logo";
            pictureBox_Logo.Size = new Size(50, 50);
            pictureBox_Logo.TabIndex = 0;
            pictureBox_Logo.TabStop = false;
            // 
            // panel_Content
            // 
            panel_Content.BackColor = Color.White;
            panel_Content.Controls.Add(groupBox_PlatformSelection);
            panel_Content.Dock = DockStyle.Fill;
            panel_Content.Location = new Point(3, 103);
            panel_Content.Name = "panel_Content";
            panel_Content.Padding = new Padding(25);
            panel_Content.Size = new Size(514, 144);
            panel_Content.TabIndex = 1;
            // 
            // groupBox_PlatformSelection
            // 
            groupBox_PlatformSelection.Controls.Add(comboBox_Platform);
            groupBox_PlatformSelection.Dock = DockStyle.Fill;
            groupBox_PlatformSelection.Font = new Font("微软雅黑", 11F, FontStyle.Bold);
            groupBox_PlatformSelection.ForeColor = Color.FromArgb(52, 144, 220);
            groupBox_PlatformSelection.Location = new Point(25, 25);
            groupBox_PlatformSelection.Name = "groupBox_PlatformSelection";
            groupBox_PlatformSelection.Padding = new Padding(20);
            groupBox_PlatformSelection.Size = new Size(464, 94);
            groupBox_PlatformSelection.TabIndex = 0;
            groupBox_PlatformSelection.TabStop = false;
            groupBox_PlatformSelection.Text = "选择聊天平台";
            // 
            // comboBox_Platform
            // 
            comboBox_Platform.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            comboBox_Platform.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox_Platform.Font = new Font("微软雅黑", 11F);
            comboBox_Platform.FormattingEnabled = true;
            comboBox_Platform.Location = new Point(22, 37);
            comboBox_Platform.Name = "comboBox_Platform";
            comboBox_Platform.Size = new Size(421, 28);
            comboBox_Platform.TabIndex = 0;
            comboBox_Platform.SelectedIndexChanged += ComboBox_Platform_SelectedIndexChanged;
            // 
            // panel_Footer
            // 
            panel_Footer.BackColor = Color.FromArgb(248, 249, 250);
            panel_Footer.Controls.Add(button_Cancel);
            panel_Footer.Controls.Add(button_Confirm);
            panel_Footer.Dock = DockStyle.Fill;
            panel_Footer.Location = new Point(3, 253);
            panel_Footer.Name = "panel_Footer";
            panel_Footer.Padding = new Padding(25, 15, 25, 15);
            panel_Footer.Size = new Size(514, 64);
            panel_Footer.TabIndex = 2;
            // 
            // button_Cancel
            // 
            button_Cancel.Anchor = AnchorStyles.Right;
            button_Cancel.BackColor = Color.FromArgb(108, 117, 125);
            button_Cancel.FlatAppearance.BorderSize = 0;
            button_Cancel.FlatStyle = FlatStyle.Flat;
            button_Cancel.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            button_Cancel.ForeColor = Color.White;
            button_Cancel.Location = new Point(309, 17);
            button_Cancel.Name = "button_Cancel";
            button_Cancel.Size = new Size(100, 35);
            button_Cancel.TabIndex = 1;
            button_Cancel.Text = "取消";
            button_Cancel.UseVisualStyleBackColor = false;
            button_Cancel.Click += Button_Cancel_Click;
            // 
            // button_Confirm
            // 
            button_Confirm.Anchor = AnchorStyles.Right;
            button_Confirm.BackColor = Color.FromArgb(52, 144, 220);
            button_Confirm.FlatAppearance.BorderSize = 0;
            button_Confirm.FlatStyle = FlatStyle.Flat;
            button_Confirm.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            button_Confirm.ForeColor = Color.White;
            button_Confirm.Location = new Point(415, 17);
            button_Confirm.Name = "button_Confirm";
            button_Confirm.Size = new Size(100, 35);
            button_Confirm.TabIndex = 0;
            button_Confirm.Text = "确认";
            button_Confirm.UseVisualStyleBackColor = false;
            button_Confirm.Click += Button_Confirm_Click;
            // 
            // FormStartup
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(520, 320);
            Controls.Add(tableLayoutPanel_Main);
            Font = new Font("微软雅黑", 9F);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "FormStartup";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "请选择要使用的聊天平台";
            tableLayoutPanel_Main.ResumeLayout(false);
            panel_Header.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBox_Logo).EndInit();
            panel_Content.ResumeLayout(false);
            groupBox_PlatformSelection.ResumeLayout(false);
            panel_Footer.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Main;
        private Panel panel_Header;
        private Label label_Title;
        private PictureBox pictureBox_Logo;
        private System.Windows.Forms.Panel panel_Content;
        private System.Windows.Forms.GroupBox groupBox_PlatformSelection;
        private System.Windows.Forms.ComboBox comboBox_Platform;
        private System.Windows.Forms.Panel panel_Footer;
        private Button button_Cancel;
        private Button button_Confirm;
    }
}

using CommandGuard.Enums;

namespace CommandGuard.Helpers;

/// <summary>
/// 彩种管理辅助类
/// 提供彩种相关的静态方法和常量定义
/// </summary>
public static class LotteryHelper
{
    #region 彩种信息定义

    /// <summary>
    /// 彩种信息结构
    /// </summary>
    public record LotteryInfo(
        EnumLottery Type,
        string DisplayName,
        string Description,
        string CalculationMethod
    );

    /// <summary>
    /// 所有支持的彩种信息
    /// </summary>
    public static readonly Dictionary<EnumLottery, LotteryInfo> LotteryInfos = new()
    {
        {
            EnumLottery.宾果1,
            new LotteryInfo(
                EnumLottery.宾果1,
                @"宾果1",
                @"直接采用第21个号码除以4来计算番摊",
                @"第21个号码 ÷ 4"
            )
        },
        {
            EnumLottery.宾果2,
            new LotteryInfo(
                EnumLottery.宾果2,
                @"宾果2",
                @"取第1个+第20个+第21个之和除以4来计算番摊",
                @"(第1个 + 第20个 + 第21个) ÷ 4"
            )
        },
        {
            EnumLottery.宾果3,
            new LotteryInfo(
                EnumLottery.宾果3,
                @"宾果3",
                @"取前20个号码之和除以4来计算番摊（原台湾宾果）",
                @"前20个号码之和 ÷ 4"
            )
        }
    };

    #endregion

    #region 公共方法

    /// <summary>
    /// 获取彩种的显示名称
    /// </summary>
    /// <param name="lottery">彩种类型</param>
    /// <returns>显示名称</returns>
    public static string GetDisplayName(EnumLottery lottery)
    {
        return LotteryInfos.TryGetValue(lottery, out var info) ? info.DisplayName : @"未知彩种";
    }

    /// <summary>
    /// 获取彩种的描述
    /// </summary>
    /// <param name="lottery">彩种类型</param>
    /// <returns>彩种描述</returns>
    public static string GetDescription(EnumLottery lottery)
    {
        return LotteryInfos.TryGetValue(lottery, out var info) ? info.Description : @"未知彩种";
    }

    /// <summary>
    /// 获取彩种的计算方法说明
    /// </summary>
    /// <param name="lottery">彩种类型</param>
    /// <returns>计算方法说明</returns>
    public static string GetCalculationMethod(EnumLottery lottery)
    {
        return LotteryInfos.TryGetValue(lottery, out var info) ? info.CalculationMethod : @"未知计算方法";
    }

    /// <summary>
    /// 获取所有支持的彩种列表
    /// </summary>
    /// <returns>彩种列表</returns>
    public static List<EnumLottery> GetSupportedLotteries()
    {
        return [EnumLottery.宾果1, EnumLottery.宾果2, EnumLottery.宾果3];
    }

    /// <summary>
    /// 获取所有彩种信息列表
    /// </summary>
    /// <returns>彩种信息列表</returns>
    public static List<LotteryInfo> GetAllLotteryInfos()
    {
        return LotteryInfos.Values.ToList();
    }

    /// <summary>
    /// 验证彩种是否有效
    /// </summary>
    /// <param name="lottery">彩种类型</param>
    /// <returns>是否有效</returns>
    public static bool IsValidLottery(EnumLottery lottery)
    {
        return LotteryInfos.ContainsKey(lottery);
    }

    /// <summary>
    /// 根据整数值获取彩种枚举
    /// </summary>
    /// <param name="value">整数值</param>
    /// <returns>彩种枚举，如果无效则返回null</returns>
    public static EnumLottery? GetLotteryFromInt(int value)
    {
        if (Enum.IsDefined(typeof(EnumLottery), value))
        {
            return (EnumLottery)value;
        }
        return null;
    }

    /// <summary>
    /// 获取彩种的整数值
    /// </summary>
    /// <param name="lottery">彩种类型</param>
    /// <returns>整数值</returns>
    public static int GetLotteryIntValue(EnumLottery lottery)
    {
        return (int)lottery;
    }

    #endregion

    #region 番摊计算相关

    /// <summary>
    /// 验证号码数量是否满足彩种要求
    /// </summary>
    /// <param name="lottery">彩种类型</param>
    /// <param name="numberCount">号码数量</param>
    /// <returns>是否满足要求</returns>
    public static bool ValidateNumberCount(EnumLottery lottery, int numberCount)
    {
        return lottery switch
        {
            EnumLottery.宾果1 => numberCount >= 21, // 需要第21个号码
            EnumLottery.宾果2 => numberCount >= 21, // 需要第1、20、21个号码
            EnumLottery.宾果3 => numberCount >= 20, // 需要前20个号码
            _ => false
        };
    }

    /// <summary>
    /// 获取彩种所需的最少号码数量
    /// </summary>
    /// <param name="lottery">彩种类型</param>
    /// <returns>最少号码数量</returns>
    public static int GetRequiredNumberCount(EnumLottery lottery)
    {
        return lottery switch
        {
            EnumLottery.宾果1 => 21,
            EnumLottery.宾果2 => 21,
            EnumLottery.宾果3 => 20,
            _ => 0
        };
    }

    #endregion
}

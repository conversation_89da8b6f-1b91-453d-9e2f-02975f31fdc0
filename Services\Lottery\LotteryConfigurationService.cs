﻿using CommandGuard.Enums;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Interfaces.Lottery;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Lottery;

/// <summary>
/// 彩票配置服务实现 - 支持多彩种动态配置
/// 支持宾果1、宾果2、宾果3三种彩种的配置管理
/// 通过系统设置服务动态获取和设置当前彩种
/// </summary>
public class LotteryConfigurationService(
    ILogger<LotteryConfigurationService> logger,
    ISystemSettingService systemSettingService) : ILotteryConfigurationService
{
    #region 常量定义

    /// <summary>
    /// 系统设置中彩种选择的键名
    /// </summary>
    private const string CurrentLotterySettingKey = @"当前彩种";

    /// <summary>
    /// 默认彩种（宾果3，保持向后兼容）
    /// </summary>
    private const EnumLottery DefaultLottery = EnumLottery.宾果3;

    #endregion

    #region 异步方法实现

    /// <summary>
    /// 获取当前彩票游戏类型
    /// 从系统设置中动态获取当前选择的彩种
    /// </summary>
    public async Task<EnumLottery> GetCurrentLotteryAsync()
    {
        try
        {
            var lotteryValue = await systemSettingService.GetSettingValueAsync(CurrentLotterySettingKey, (int)DefaultLottery);

            if (Enum.IsDefined(typeof(EnumLottery), lotteryValue))
            {
                return (EnumLottery)lotteryValue;
            }

            logger.LogWarning(@"系统设置中的彩种值无效: {Value}，使用默认彩种: {DefaultLottery}", lotteryValue, DefaultLottery);
            return DefaultLottery;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取当前彩种失败，使用默认彩种: {DefaultLottery}", DefaultLottery);
            return DefaultLottery;
        }
    }

    /// <summary>
    /// 设置当前彩票游戏类型
    /// </summary>
    public async Task SetCurrentLotteryAsync(EnumLottery lottery)
    {
        try
        {
            var success = await systemSettingService.SetSettingValueAsync(CurrentLotterySettingKey, (int)lottery);
            if (success)
            {
                logger.LogInformation(@"彩种设置已更新: {Lottery}", lottery);
            }
            else
            {
                logger.LogWarning(@"彩种设置更新失败: {Lottery}", lottery);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"设置当前彩种失败: {Lottery}", lottery);
            throw;
        }
    }

    /// <summary>
    /// 获取指定彩种的空开奖号码字符串
    /// </summary>
    public async Task<string> GetEmptyDrawNumbersAsync(EnumLottery? lottery = null)
    {
        lottery ??= await GetCurrentLotteryAsync();

        // 所有宾果彩种都使用21个号码
        return @"0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0";
    }

    /// <summary>
    /// 获取指定彩种的最大数据条数
    /// </summary>
    public async Task<int> GetMaxDataCountAsync(EnumLottery? lottery = null)
    {
        lottery ??= await GetCurrentLotteryAsync();

        // 所有宾果彩种都使用相同的最大数据条数
        return 20;
    }

    /// <summary>
    /// 获取指定彩种的数据查询标识
    /// </summary>
    public async Task<string> GetDataQueryFlagAsync(Models.KaiJiang kaiJiang, EnumLottery? lottery = null)
    {
        lottery ??= await GetCurrentLotteryAsync();

        // 所有宾果彩种都使用时间前10位（日期）作为查询标识
        return kaiJiang.Time.Substring(0, 10);
    }

    /// <summary>
    /// 判断指定彩种是否使用时间查询
    /// </summary>
    public async Task<bool> UseTimeQueryAsync(EnumLottery? lottery = null)
    {
        lottery ??= await GetCurrentLotteryAsync();

        // 所有宾果彩种都使用时间查询
        return true;
    }

    /// <summary>
    /// 获取彩种的显示名称
    /// </summary>
    public string GetLotteryDisplayName(EnumLottery lottery)
    {
        return lottery switch
        {
            EnumLottery.宾果1 => @"宾果1",
            EnumLottery.宾果2 => @"宾果2",
            EnumLottery.宾果3 => @"宾果3",
            _ => @"未知彩种"
        };
    }

    /// <summary>
    /// 获取所有支持的彩种列表
    /// </summary>
    public List<EnumLottery> GetSupportedLotteries()
    {
        return [EnumLottery.宾果1, EnumLottery.宾果2, EnumLottery.宾果3];
    }

    #endregion

    #region 兼容性方法（保持向后兼容）

    /// <summary>
    /// 获取空开奖号码字符串（兼容性方法）
    /// 同步调用异步方法，使用默认彩种
    /// </summary>
    public string GetEmptyDrawNumbers()
    {
        try
        {
            return GetEmptyDrawNumbersAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取空开奖号码字符串失败，使用默认值");
            return @"0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0";
        }
    }

    /// <summary>
    /// 获取最大数据条数（兼容性方法）
    /// 同步调用异步方法，使用默认彩种
    /// </summary>
    public int GetMaxDataCount()
    {
        try
        {
            return GetMaxDataCountAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取最大数据条数失败，使用默认值");
            return 20;
        }
    }

    /// <summary>
    /// 获取数据查询标识（兼容性方法）
    /// 同步调用异步方法，使用默认彩种
    /// </summary>
    public string GetDataQueryFlag(Models.KaiJiang kaiJiang)
    {
        try
        {
            return GetDataQueryFlagAsync(kaiJiang).GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取数据查询标识失败，使用默认逻辑");
            return kaiJiang.Time.Substring(0, 10);
        }
    }

    /// <summary>
    /// 判断是否使用时间查询（兼容性方法）
    /// 同步调用异步方法，使用默认彩种
    /// </summary>
    public bool UseTimeQuery()
    {
        try
        {
            return UseTimeQueryAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"判断是否使用时间查询失败，使用默认值");
            return true;
        }
    }

    #endregion
}
using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 上下分记录页面
/// </summary>
public partial class FormMain
{
    #region 上下分记录查询界面事件处理

    /// <summary>
    /// 上下分记录查询按钮点击事件处理
    /// </summary>
    private async void button_根据条件查询上下分记录_Click(object sender, EventArgs e)
    {
        try
        {
            // 禁用查询按钮，防止重复点击
            button_根据条件查询上下分记录.Enabled = false;
            button_根据条件查询上下分记录.Text = @"查询中...";

            // 获取查询条件
            var queryConditions = GetDepositWithdrawQueryConditions();

            // 验证查询条件
            if (!ValidateDepositWithdrawQueryConditions(queryConditions))
            {
                return;
            }

            // 执行查询
            await QueryDepositWithdrawRecordsAsync(
                queryConditions.StartTime,
                queryConditions.EndTime,
                queryConditions.Account,
                queryConditions.Type,
                queryConditions.IncludeFakeUsers
            );

            _logger.LogInformation(@"上下分记录查询完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询上下分记录时发生错误");
            MessageBox.Show($@"查询失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 恢复查询按钮状态
            button_根据条件查询上下分记录.Enabled = true;
            button_根据条件查询上下分记录.Text = @"查询";
        }
    }

    /// <summary>
    /// 获取上下分记录查询条件
    /// </summary>
    private DepositWithdrawQueryConditions GetDepositWithdrawQueryConditions()
    {
        try
        {
            // 获取开始时间
            var startDate = dateTimePicker_查询上下分记录开始日期.Value.Date;
            var startHour = (int)numericUpDown_查询上下分记录开始小时.Value;
            var startMinute = (int)numericUpDown_查询上下分记录开始分钟.Value;
            var startTime = startDate.AddHours(startHour).AddMinutes(startMinute);

            // 获取结束时间
            var endDate = dateTimePicker_查询上下分记录结束日期.Value.Date;
            var endHour = (int)numericUpDown_查询上下分记录结束小时.Value;
            var endMinute = (int)numericUpDown_查询上下分记录结束分钟.Value;
            var endTime = endDate.AddHours(endHour).AddMinutes(endMinute);

            // 获取其他条件
            var account = textBox_查询上下分记录账号.Text.Trim();
            var selectedType = comboBox__查询上下分记录类型.SelectedItem?.ToString() ?? @"全部";
            var type = selectedType == @"全部" ? string.Empty : selectedType;
            var includeFakeUsers = checkBox_查询上下分记录包含假人.Checked;

            return new DepositWithdrawQueryConditions
            {
                StartTime = startTime,
                EndTime = endTime,
                Account = string.IsNullOrEmpty(account) ? null : account,
                Type = string.IsNullOrEmpty(type) ? null : type,
                IncludeFakeUsers = includeFakeUsers
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"获取上下分记录查询条件时发生错误");
            throw new InvalidOperationException(@"获取查询条件失败", ex);
        }
    }

    /// <summary>
    /// 验证上下分记录查询条件
    /// </summary>
    private bool ValidateDepositWithdrawQueryConditions(DepositWithdrawQueryConditions conditions)
    {
        try
        {
            // 使用查询条件模型的验证方法
            var (isValid, errorMessage) = conditions.Validate();

            if (!isValid)
            {
                MessageBox.Show(errorMessage, @"查询条件错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 验证时间跨度（防止查询时间过长）
            var timeSpan = conditions.EndTime - conditions.StartTime;
            if (timeSpan.TotalDays > 31)
            {
                var result = MessageBox.Show(
                    $@"查询时间跨度为{timeSpan.TotalDays:F1}天，可能会影响查询性能。是否继续？",
                    @"查询确认",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                {
                    return false;
                }
            }

            // 验证类型格式（如果提供）
            if (conditions.HasTypeFilter)
            {
                if (conditions.Type != @"上分" && conditions.Type != @"下分" && conditions.Type != @"回水")
                {
                    MessageBox.Show(@"类型只能是'上分'、'下分'或'回水'", @"查询条件错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"验证上下分记录查询条件时发生错误");
            MessageBox.Show(@"验证查询条件时发生错误", @"系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }

    /// <summary>
    /// 上下分记录查询文本框键盘事件处理
    /// 支持Enter键快捷查询
    /// </summary>
    private void DepositWithdrawQueryTextBox_KeyDown(object? sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true; // 阻止默认的Enter键行为

            // 触发查询
            Task.Run(() =>
            {
                // 模拟点击查询按钮
                Invoke(() => { button_根据条件查询上下分记录.PerformClick(); });
            });
        }
    }

    #endregion

    #region 上下分记录查询功能

    /// <summary>
    /// 查询上下分记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">账号（可选）</param>
    /// <param name="type">类型（可选）</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    public async Task QueryDepositWithdrawRecordsAsync(DateTime startTime, DateTime endTime, string? account = null, string? type = null, bool includeFakeUsers = false)
    {
        try
        {
            _logger.LogInformation(@"开始查询上下分记录，时间范围: {StartTime} - {EndTime}", startTime, endTime);

            // 确保上下分记录表格已初始化
            // EnsureDepositWithdrawRecordGridInitialized();

            // 查询上下分记录
            _depositWithdrawRecordViewModels = await _depositWithdrawRecordService.QueryDepositWithdrawRecordsAsync(startTime, endTime, account, type, includeFakeUsers);

            // 更新UI（确保在UI线程中执行）
            if (InvokeRequired)
            {
                Invoke(() =>
                {
                    _depositWithdrawRecordBindingSource.DataSource = _depositWithdrawRecordViewModels;
                    _depositWithdrawRecordBindingSource.ResetBindings(false);

                    // 清除选择
                    dataGridView_上下分记录.ClearSelection();

                    // 更新统计信息
                    UpdateDepositWithdrawRecordStatistics();

                    _logger.LogInformation(@"上下分记录查询完成，共 {Count} 条记录", _depositWithdrawRecordViewModels.Count);
                });
            }
            else
            {
                _depositWithdrawRecordBindingSource.DataSource = _depositWithdrawRecordViewModels;
                _depositWithdrawRecordBindingSource.ResetBindings(false);

                // 清除选择
                dataGridView_上下分记录.ClearSelection();

                // 更新统计信息
                UpdateDepositWithdrawRecordStatistics();

                _logger.LogInformation(@"上下分记录查询完成，共 {Count} 条记录", _depositWithdrawRecordViewModels.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询上下分记录失败");
            MessageBox.Show($@"查询上下分记录失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 更新上下分记录统计信息
    /// </summary>
    private async void UpdateDepositWithdrawRecordStatistics()
    {
        try
        {
            var (totalDeposit, totalWithdraw, totalRebate, totalBalance) = await _depositWithdrawRecordService.GetDepositWithdrawStatisticsAsync(_depositWithdrawRecordViewModels);

            // 更新标签显示
            label_显示查询上下分统计数据.Text = $@"总上分: {totalDeposit:F2} | 总下分: {totalWithdraw:F2} | 总回水: {totalRebate:F2} | 总积分: {totalBalance:F2} | 记录数: {_depositWithdrawRecordViewModels.Count}";

            _logger.LogDebug(@"上下分记录统计更新 - 总上分: {TotalDeposit:F2}, 总下分: {TotalWithdraw:F2}, 总回水: {TotalRebate:F2}, 总积分: {TotalBalance:F2}",
                totalDeposit, totalWithdraw, totalRebate, totalBalance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新上下分记录统计信息失败");
            label_显示查询上下分统计数据.Text = @"统计信息加载失败";
        }
    }

    /// <summary>
    /// 示例：查询今日上下分记录
    /// </summary>
    public async void QueryTodayDepositWithdrawRecords()
    {
        var startTime = DateTime.Today;
        var endTime = DateTime.Today.AddDays(1).AddSeconds(-1);
        await QueryDepositWithdrawRecordsAsync(startTime, endTime, includeFakeUsers: true);
    }

    /// <summary>
    /// 示例：查询指定条件的上下分记录
    /// </summary>
    public async void QueryDepositWithdrawRecordsWithConditions(DateTime startTime, DateTime endTime, string? account = null, string? type = null, bool includeFakeUsers = false)
    {
        await QueryDepositWithdrawRecordsAsync(startTime, endTime, account, type, includeFakeUsers);
    }

    /// <summary>
    /// 快捷查询：查询昨天的上下分记录
    /// </summary>
    public async void QueryYesterdayDepositWithdrawRecords()
    {
        var yesterday = DateTime.Today.AddDays(-1);
        await QueryDepositWithdrawRecordsAsync(yesterday, yesterday.AddDays(1).AddSeconds(-1), includeFakeUsers: true);
    }

    /// <summary>
    /// 快捷查询：查询本周的上下分记录
    /// </summary>
    public async void QueryThisWeekDepositWithdrawRecords()
    {
        var today = DateTime.Today;
        var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
        await QueryDepositWithdrawRecordsAsync(startOfWeek, today.AddDays(1).AddSeconds(-1), includeFakeUsers: true);
    }

    /// <summary>
    /// 快捷查询：查询指定用户的上下分记录
    /// </summary>
    /// <param name="account">用户账号</param>
    public async void QueryUserDepositWithdrawRecords(string account)
    {
        var today = DateTime.Today;
        await QueryDepositWithdrawRecordsAsync(today.AddDays(-7), today.AddDays(1).AddSeconds(-1), account: account, includeFakeUsers: true);
    }

    /// <summary>
    /// 快捷查询：查询指定类型的上下分记录
    /// </summary>
    /// <param name="type">类型（"上分"或"下分"）</param>
    public async void QueryDepositWithdrawRecordsByType(string type)
    {
        var today = DateTime.Today;
        await QueryDepositWithdrawRecordsAsync(today.AddDays(-7), today.AddDays(1).AddSeconds(-1), type: type, includeFakeUsers: true);
    }

    /// <summary>
    /// 快捷查询：只查询上分记录
    /// </summary>
    public void QueryDepositRecordsOnly()
    {
        QueryDepositWithdrawRecordsByType(@"上分");
    }

    /// <summary>
    /// 快捷查询：只查询下分记录
    /// </summary>
    public void QueryWithdrawRecordsOnly()
    {
        QueryDepositWithdrawRecordsByType(@"下分");
    }

    #endregion
}
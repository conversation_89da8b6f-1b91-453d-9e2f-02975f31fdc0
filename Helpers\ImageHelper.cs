﻿using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using AiHelper;
using CommandGuard.Constants;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using CommandGuard.Services.Lottery;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Helpers;

/// <summary>
/// 图片服务 - 负责生成开奖数据和走势图
/// </summary>
public class ImageHelper(
    ILogger<ImageHelper> logger,
    IFreeSql fSql,
    ILotteryConfigurationService lotteryConfig,
    ISystemSettingService systemSettingService,
    SettlementService settlementService)
{
    #region 静态资源缓存

    /// <summary>
    /// 检查台湾宾果3是否启用
    /// 系统专门为台湾宾果3设计，固定返回true
    /// </summary>
    /// <returns>固定返回true</returns>
    private Task<bool> IsTaiwanBingo3EnabledAsync()
    {
        return Task.FromResult(true);
    }

    #endregion

    #region 私有通用方法

    /// <summary>
    /// 获取并处理开奖数据
    /// 根据游戏类型获取指定时间段或期号段的开奖数据，并进行数据补全和数量限制处理
    /// </summary>
    /// <param name="kj">基准开奖数据，用于确定查询范围</param>
    /// <param name="emptyDrawNum">空开奖号码字符串，用于补全缺失数据</param>
    /// <returns>处理后的开奖数据列表，按期号倒序排列</returns>
    private async Task<List<KaiJiang>> GetProcessedKaiJiangData(KaiJiang kj, string emptyDrawNum)
    {
        // 根据游戏类型确定数据查询标识
        // 台湾宾果：使用时间前10位（日期）作为查询条件
        // 168飞艇：使用期号前8位作为查询条件
        string getDataFlag = lotteryConfig.GetDataQueryFlag(kj);

        // 从数据库查询对应时间段或期号段的开奖数据
        var kjList = await fSql.Select<KaiJiang>()
            .Where(x => lotteryConfig.UseTimeQuery()
                ? x.Time.StartsWith(getDataFlag)
                : x.Issue.StartsWith(getDataFlag))
            .ToListAsync();

        // 按期号升序排列
        kjList = kjList.OrderBy(x => x.Issue).ToList();

        // 补全缺失的期号数据
        FillEmptyData(kjList, emptyDrawNum);
        return kjList;
    }

    /// <summary>
    /// 补全空数据
    /// 检查开奖数据列表中是否有缺失的期号，如果有则插入空数据进行补全
    /// 确保期号连续性，避免图表显示时出现空白或错位
    /// </summary>
    /// <param name="kjList">开奖数据列表</param>
    /// <param name="emptyDrawNum">空开奖号码字符串</param>
    private static void FillEmptyData(List<KaiJiang> kjList, string emptyDrawNum)
    {
        for (int i = 0; i < kjList.Count - 1; i++)
        {
            // 检查相邻两个期号是否连续
            if (Convert.ToInt64(kjList[i].Issue) + 1 != Convert.ToInt64(kjList[i + 1].Issue))
            {
                // 插入缺失的期号数据
                kjList.Insert(i + 1, new KaiJiang
                {
                    Issue = (Convert.ToInt64(kjList[i].Issue) + 1).ToString(),
                    DrawNum = emptyDrawNum,
                    Time = Ai.GetTextLeft(kjList[i].Time, " ") + " " + @"00:00:00"
                });
                i--; // 重新检查当前位置，确保连续补全
            }
        }
    }

    /// <summary>
    /// 合并多张图片为一张垂直排列的图片
    /// 将多个游戏类型的图片垂直合并成一张完整的图片
    /// </summary>
    /// <param name="images">要合并的图片列表</param>
    /// <returns>合并后的图片</returns>
    private static Task<Bitmap> MergeImages(List<Bitmap> images)
    {
        if (!images.Any()) return Task.FromResult(new Bitmap(1, 1));

        // 计算合并后图片的总高度和最大宽度
        int totalHeight = images.Sum(img => img.Height);
        int maxWidth = images.Max(img => img.Width);

        // 创建合并后的图片画布
        var mergedImage = new Bitmap(maxWidth, totalHeight);
        using var g = Graphics.FromImage(mergedImage);

        // 垂直排列绘制每张图片
        int currentHeight = 0;
        foreach (var img in images)
        {
            g.DrawImage(img, new Rectangle(0, currentHeight, img.Width, img.Height));
            currentHeight += img.Height;
        }

        return Task.FromResult(mergedImage);
    }

    /// <summary>
    /// 图片缩放处理
    /// 将原始图片按指定比例缩放，并添加随机透明图形以增加图片的不可预测性
    /// 主要用于减小图片文件大小，便于网络传输和存储
    /// 同时通过添加随机元素，避免图片被简单的数字指纹识别
    /// </summary>
    /// <param name="original">原始位图对象</param>
    /// <param name="scaleFactor">缩放比例，范围0.0-1.0，例如0.25表示缩放到原尺寸的25%</param>
    /// <returns>缩放后的位图对象</returns>
    private static Task<Bitmap> ChangeScaledImage(Bitmap original, float scaleFactor)
    {
        // 计算缩放后的新尺寸
        int newWidth = (int)(original.Width * scaleFactor);
        int newHeight = (int)(original.Height * scaleFactor);

        // 创建新的位图画布
        var scaledBmp = new Bitmap(newWidth, newHeight);
        using var g = Graphics.FromImage(scaledBmp);

        // 设置高质量的插值模式，确保缩放后图片保持清晰
        // HighQualityBicubic：使用高质量双三次插值算法，适合图片缩放
        g.InterpolationMode = InterpolationMode.HighQualityBicubic;
        g.DrawImage(original, 0, 0, newWidth, newHeight);

        // 添加随机透明图形，增加图片的不可预测性
        // 这些图形完全透明（Alpha=0），不影响视觉效果
        // 但会改变图片的数字指纹，防止被简单的图片识别算法检测
        var rand = new Random();
        for (int i = 0; i < 100; i++)
        {
            // 随机生成矩形的位置和尺寸
            int x = rand.Next(newWidth);
            int y = rand.Next(newHeight);
            int width = rand.Next(1, 10); // 小尺寸矩形，减少对性能的影响
            int height = rand.Next(1, 10);

            // 创建完全透明的随机颜色（Alpha=0表示完全透明）
            var color = Color.FromArgb(0, rand.Next(256), rand.Next(256), rand.Next(256));
            using var pen = new Pen(color);
            g.DrawRectangle(pen, x, y, width, height);
        }

        return Task.FromResult(scaledBmp);
    }

    #endregion

    #region 公共方法-绘制开奖图

    /// <summary>
    /// 绘制开奖数据图片
    /// 生成包含最近开奖数据的详细图片，显示期号、时间、开奖号码、总和、番摊结果和大小单双
    /// 支持多种游戏类型同时生成，最终合并为一张图片
    /// </summary>
    /// <param name="lastKj">基准开奖数据，用于确定查询范围</param>
    /// <returns>异步任务</returns>
    public async Task DrawOpenDataAsync(KaiJiang lastKj)
    {
        try
        {
            // 根据游戏类型确定数据参数
            // 台湾宾果：最多20条数据，21个0的空号码
            // 168飞艇：最多6条数据，10个0的空号码
            var maxCount = lotteryConfig.GetMaxDataCount();
            var emptyDrawNum = lotteryConfig.GetEmptyDrawNumbers();

            // 获取处理后的开奖数据
            var kjList = await GetProcessedKaiJiangData(lastKj, emptyDrawNum);

            // 删除多余数据
            while (kjList.Count > maxCount)
            {
                kjList.RemoveAt(0);
            }

            // 开奖图需要倒序排列，最新数据在前
            kjList.Reverse();

            // 处理画图数据
            List<LotteryResult> lotteryResults = new();
            foreach (KaiJiang kj in kjList)
            {
                // 解析开奖号码并计算总和（用于显示）
                var (numbers, isValid) = settlementService.ParseTaiwanBingoNumbers(kj.DrawNum);
                var sum = isValid ? numbers.Sum() : 0;
                lotteryResults.Add(new LotteryResult(kj.Issue, sum % 100));
            }

            // 先随机取2个1至13之间不重复的数字
            var random = new Random();
            var indexs = new List<int>();

            for (int i = 1; i <= 13; i++)
            {
                if (indexs.Count < 2)
                {
                    indexs.Add(i);
                }
                else
                {
                    indexs[random.Next(0, 2)] = i;
                }
            }

            string? bannerPathTop = $"img/banner_{indexs[0]}.png";
            string? bannerPathButtom = $"img/banner_{indexs[1]}.png";
            SystemSetting? banner = await systemSettingService.GetSettingByKeyAsync("开启图片背景");
            if (banner is { SettingValue: "False" })
            {
                bannerPathTop = null;
                bannerPathButtom = null;
            }

            using Bitmap perfectImage = EnhancedLotteryTableDrawer.DrawPerfectLotteryTable(lotteryResults, bannerPathTop, bannerPathButtom);

            // 保存高质量图片
            perfectImage.Save(ImageConstants.DrawImagePath, ImageFormat.Png);
            Console.WriteLine($@"完美彩票表格图片已生成: {ImageConstants.DrawImagePath}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DrawOpenDataAsync执行失败");
        }
    }

    /// <summary>
    /// 创建台湾宾果3的开奖数据图片
    /// 生成包含标题和多行开奖数据的图片
    /// </summary>
    /// <param name="kjList">开奖数据列表</param>
    /// <param name="emptyDrawNum">空开奖号码字符串</param>
    /// <returns>生成的图片，如果数据为空则返回null</returns>
    private Bitmap? CreateOpenDataBitmap(List<KaiJiang> kjList, string emptyDrawNum)
    {
        if (!kjList.Any()) return null;

        // 创建画布，高度根据数据行数动态计算
        var bmp = new Bitmap(2500, 130 + 145 * kjList.Count);
        using var g = Graphics.FromImage(bmp);
        g.Clear(Color.White);

        // 绘制头部标题背景
        using var titleImg = Image.FromFile(ImageConstants.ImagePaths["title"]);
        g.DrawImage(titleImg, 0, 0, titleImg.Width, titleImg.Height);

        // 绘制每行开奖数据
        for (int i = 0; i < kjList.Count; i++)
        {
            DrawOpenDataRow(g, kjList[i], i, emptyDrawNum);
        }

        return bmp;
    }

    /// <summary>
    /// 绘制单行开奖数据
    /// 在指定位置绘制一期开奖数据的详细信息
    /// </summary>
    /// <param name="g">图形绘制对象</param>
    /// <param name="kj">开奖数据</param>
    /// <param name="rowIndex">行索引</param>
    /// <param name="emptyDrawNum">空开奖号码字符串</param>
    private void DrawOpenDataRow(Graphics g, KaiJiang kj, int rowIndex, string emptyDrawNum)
    {
        int y = 130 + 145 * rowIndex; // 计算当前行的Y坐标

        // 绘制详情栏背景图
        using var detailImg = Image.FromFile(ImageConstants.ImagePaths["detail"]);
        g.DrawImage(detailImg, 0, y, detailImg.Width, detailImg.Height);

        // 设置字体和颜色
        using var font = new Font("微软雅黑", 40, FontStyle.Bold);
        var textColor = new SolidBrush(Color.FromArgb(255, 79, 38, 13));

        // 绘制开奖期号
        g.DrawString(kj.Issue, font, textColor, 50, y + 30);

        // 检查是否有有效的开奖数据
        if (kj.DrawNum == emptyDrawNum) return; // 如果是空数据则跳过

        // 绘制开奖时间（去掉秒数）
        string? tmpTime = Ai.GetTextRight(kj.Time, "-");
        tmpTime = tmpTime.Substring(0, tmpTime.Length - 3);
        g.DrawString(tmpTime, font, textColor, 500, y + 30);

        // 获取台湾宾果3的开奖号码和结果（直接使用SettlementService）
        string drawNum = settlementService.ExtractNumberString(kj.DrawNum, 20);
        int drawResult = settlementService.CalculateFanTanResult(kj.DrawNum);

        // 绘制开奖号码
        // await DrawOpenDataNumbers(g, drawNum, y, font, textColor);

        // 计算并绘制号码总和（使用SettlementService统一计算）
        var (numbers, isValid) = settlementService.ParseTaiwanBingoNumbers(drawNum);
        int sum = isValid ? numbers.Sum() : 0;
        g.DrawString(sum.ToString(), font, textColor, 1865, y + 30);

        // 绘制番摊结果图标和大小单双文字
        DrawResultIcon(g, drawResult, 2010, y, font);
    }

    /// <summary>
    /// 绘制台湾宾果3开奖号码
    /// 在合适的位置绘制20个开奖号码
    /// </summary>
    /// <param name="g">图形绘制对象</param>
    /// <param name="drawNum">开奖号码字符串</param>
    /// <param name="y">绘制Y坐标</param>
    /// <param name="font">字体</param>
    /// <param name="textColor">文字颜色</param>
    private Task DrawOpenDataNumbers(Graphics g, string drawNum, int y, Font font, SolidBrush textColor)
    {
        var nums = drawNum.Split(',');

        // // 台湾宾果3显示20个号码
        // int startX = 1590;
        // for (int i = 0; i < nums.Length && i < 20; i++)
        // {
        //     using var yellowImg = Image.FromFile(ImageConstants.ImagePaths["yellow"]);
        //     g.DrawImage(yellowImg, startX, y, yellowImg.Width, yellowImg.Height);
        //     g.DrawString(nums[i], font, textColor, startX + 70, y + 30);
        //     startX += 150;
        //
        //     // 换行处理
        //     if ((i + 1) % 10 == 0)
        //     {
        //         startX = 1590;
        //         y += 100;
        //     }
        // }

        // 显示1个号码
        using var yellowImg = Image.FromFile(ImageConstants.ImagePaths["yellow"]);
        g.DrawImage(yellowImg, 1590, y, yellowImg.Width, yellowImg.Height);
        g.DrawString(nums[0], font, textColor, 1660, y + 30);

        return Task.CompletedTask;
    }

    /// <summary>
    /// 绘制开奖结果图标和大小单双文字
    /// 根据番摊结果绘制对应的图标，并在指定位置显示大小单双文字
    /// </summary>
    /// <param name="g">图形绘制对象</param>
    /// <param name="drawResult">开奖结果（1-4分别对应不同的番摊结果）</param>
    /// <param name="x">绘制起始X坐标</param>
    /// <param name="y">绘制起始Y坐标</param>
    /// <param name="font">文字字体</param>
    private void DrawResultIcon(Graphics g, int drawResult, int x, int y, Font font)
    {
        // 根据开奖结果选择对应的图标
        var iconPath = drawResult switch
        {
            1 => ImageConstants.ImagePaths["11"], // 番摊结果1
            2 => ImageConstants.ImagePaths["22"], // 番摊结果2
            3 => ImageConstants.ImagePaths["33"], // 番摊结果3
            4 => ImageConstants.ImagePaths["44"], // 番摊结果4
            _ => null
        };

        // 绘制结果图标
        if (iconPath != null)
        {
            using var img = Image.FromFile(iconPath);
            g.DrawImage(img, x, y, img.Width, img.Height);
        }

        // 确定大小文字和颜色（1,2为小；3,4为大）
        var (sizeText, sizeColor) = drawResult switch
        {
            1 or 2 => ("小", Color.FromArgb(255, 79, 38, 13)),
            3 or 4 => ("大", Color.FromArgb(255, 255, 0, 0)),
            _ => ("", Color.Black)
        };

        // 确定单双文字和颜色（1,3为单；2,4为双）
        var (parityText, parityColor) = drawResult switch
        {
            1 or 3 => ("单", Color.FromArgb(255, 79, 38, 13)),
            2 or 4 => ("双", Color.FromArgb(255, 255, 0, 0)),
            _ => ("", Color.Black)
        };

        // 绘制大小单双文字
        if (!string.IsNullOrEmpty(sizeText))
        {
            g.DrawString(sizeText, font, new SolidBrush(sizeColor), x + 225, y + 30);
            g.DrawString(parityText, font, new SolidBrush(parityColor), x + 375, y + 30);
        }
    }

    /// <summary>
    /// 绘制路子图中的数字图标
    /// 在路子图中绘制对应数字的图标，用于显示开奖结果的走势
    /// </summary>
    /// <param name="g">图形绘制对象</param>
    /// <param name="drawResult">开奖结果数字（0-4）</param>
    /// <param name="x">绘制X坐标</param>
    /// <param name="y">绘制Y坐标</param>
    private async Task DrawNumberIconAsync(Graphics g, int drawResult, int x, int y)
    {
        try
        {
            var imgType = await systemSettingService.GetSettingValueAsync<int>("ImgType", 1);

            if (imgType == 1)
            {
                // 根据结果数字选择对应的图标
                var iconPath = drawResult switch
                {
                    0 => ImageConstants.ImagePaths["0"], // 无结果或空数据
                    1 => ImageConstants.ImagePaths["1"], // 结果1
                    2 => ImageConstants.ImagePaths["2"], // 结果2
                    3 => ImageConstants.ImagePaths["3"], // 结果3
                    4 => ImageConstants.ImagePaths["4"], // 结果4
                    _ => ImageConstants.ImagePaths["0"] // 默认显示0
                };

                using var img = Image.FromFile(iconPath);
                g.DrawImage(img, x, y, 149, img.Height);
            }
            else if (imgType == 2)
            {
                // 根据结果数字选择对应的图标
                var iconPath = drawResult switch
                {
                    0 => ImageConstants.ImagePaths["new0"], // 无结果或空数据
                    1 => ImageConstants.ImagePaths["new1"], // 结果1
                    2 => ImageConstants.ImagePaths["new2"], // 结果2
                    3 => ImageConstants.ImagePaths["new3"], // 结果3
                    4 => ImageConstants.ImagePaths["new4"], // 结果4
                    _ => ImageConstants.ImagePaths["new0"] // 默认显示0
                };

                using var img = Image.FromFile(iconPath);
                g.DrawImage(img, x, y, 149, img.Height);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"绘制数字图标失败: drawResult={DrawResult}, x={X}, y={Y}", drawResult, x, y);
        }
    }

    #endregion

    #region 公共方法-绘制路子图

    /// <summary>
    /// 绘制路子图（固定行列）
    /// 生成固定13列指定行数的路子图，显示开奖结果的走势规律
    /// 适用于标准的路子图展示，列数固定为13列
    /// </summary>
    /// <param name="lastKj">基准开奖数据，用于确定查询范围</param>
    /// <param name="rows">路子图行数</param>
    /// <returns>异步任务</returns>
    public async Task DrawTanImageAsync(KaiJiang lastKj, int rows)
    {
        try
        {
            // 根据游戏类型确定数据参数
            // 数据量 = 行数 × 11（预留一些数据确保填满网格）
            var maxCount = rows * 11;
            var emptyDrawNum = lotteryConfig.GetEmptyDrawNumbers();

            // 获取路子图专用的处理数据
            var kjList = await GetProcessedKaiJiangData(lastKj, emptyDrawNum);

            // 删除多余数据
            while (kjList.Count > maxCount)
            {
                kjList.RemoveRange(0, rows);
            }

            // 处理画图数据
            List<LotteryResult> lotteryResults = new();
            foreach (KaiJiang kj in kjList)
            {
                // 解析开奖号码并计算总和（用于显示）
                var (numbers, isValid) = settlementService.ParseTaiwanBingoNumbers(kj.DrawNum);
                var sum = isValid ? numbers.Sum() : 0;
                lotteryResults.Add(new LotteryResult(kj.Issue, sum % 100));
            }

            // 绘制路子图
            // 先随机取2个1至13之间不重复的数字
            var random = new Random();
            var indexs = new List<int>();

            for (int i = 1; i <= 13; i++)
            {
                if (indexs.Count < 2)
                {
                    indexs.Add(i);
                }
                else
                {
                    indexs[random.Next(0, 2)] = i;
                }
            }

            string? bannerPathTop = $"img/banner_{indexs[0]}.png";
            string? bannerPathButtom = $"img/banner_{indexs[1]}.png";
            SystemSetting? banner = await systemSettingService.GetSettingByKeyAsync("开启图片背景");
            if (banner is { SettingValue: "False" })
            {
                bannerPathTop = null;
                bannerPathButtom = null;
            }

            using Bitmap tanImage = EnhancedLotteryTableDrawer.DrawTanImage(lotteryResults, @"宾果3", rows, bannerPathTop, bannerPathButtom);
            string fileName = rows.Equals(7) ? ImageConstants.TanRows7ImagePath : ImageConstants.TanRows6ImagePath;

            // 保存路子图图片
            tanImage.Save(fileName, ImageFormat.Png);
            logger.LogInformation($@"路子图图片已生成: {fileName}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DrawTanImageAsync执行失败");
        }
    }

    /// <summary>
    /// 绘制完整路子图（动态列数）
    /// 生成包含当天所有开奖数据的路子图，列数根据数据量动态计算
    /// 适用于需要显示完整历史数据的场景
    /// </summary>
    /// <param name="kj">基准开奖数据，用于确定查询范围</param>
    /// <param name="rows">路子图行数</param>
    /// <returns>异步任务</returns>
    public async Task DrawTanImageFullAsync(KaiJiang kj, int rows)
    {
        var images = new List<Bitmap>();

        try
        {
            // 获取完整数据（不限制数量）
            var emptyDrawNum = lotteryConfig.GetEmptyDrawNumbers();

            // 获取当天所有开奖数据
            var kjList = await GetProcessedKaiJiangData(kj, emptyDrawNum);

            // 根据数据量和行数计算列数
            int columns = kjList.Count / rows + 1;

            // 检查台湾宾果3是否启用
            if (await IsTaiwanBingo3EnabledAsync())
            {
                // 动态列数的路子图
                var bitmap = await CreateTanImageBitmapAsync(kjList, "台湾宾果3", rows, columns, emptyDrawNum);

                if (bitmap != null)
                {
                    // 缩放图片大小
                    var scaledBitmap = await ChangeScaledImage(bitmap, 0.25f);
                    images.Add(scaledBitmap);
                    bitmap.Dispose();
                }
            }

            // 合并所有图片并保存
            if (images.Any())
            {
                var mergedImage = await MergeImages(images);
                mergedImage.Save(rows.Equals(7) ? ImageConstants.TanRows77ImagePath : ImageConstants.TanRows66ImagePath, ImageFormat.Jpeg);
                mergedImage.Dispose();
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DrawTanImageFullAsync执行失败");
        }
        finally
        {
            // 清理所有图片资源
            foreach (var img in images)
            {
                img.Dispose();
            }
        }
    }

    /// <summary>
    /// 创建台湾宾果3路子图
    /// 生成指定行列数的路子图，以网格形式显示开奖结果的走势规律
    /// 路子图是彩票分析中常用的图表，用于观察号码的出现规律和趋势
    /// </summary>
    /// <param name="kjList">开奖数据列表</param>
    /// <param name="gameName">游戏显示名称</param>
    /// <param name="rows">路子图行数</param>
    /// <param name="columns">路子图列数</param>
    /// <param name="emptyDrawNum">空开奖号码字符串</param>
    /// <returns>生成的路子图位图，如果数据为空则返回null</returns>
    private async Task<Bitmap?> CreateTanImageBitmapAsync(List<KaiJiang> kjList, string gameName, int rows, int columns, string emptyDrawNum)
    {
        if (!kjList.Any()) return null;

        // 计算画布尺寸
        // 宽度计算：左边距(5) + 列数×图标宽度(149) + 列间距(5×列数+1) + 右边距(5+5)
        int width = 5 + 149 * columns + 5 * (columns + 1) + 5 + 5;
        // 高度计算：上边距(5) + 标题高度(140) + 标题下边距(5+5) + 行数×图标高度(140) + 行间距(5×行数+1) + 下边距(5)
        int height = 5 + 140 + 5 + 5 + 140 * rows + 5 * (rows + 1) + 5;

        var bmp = new Bitmap(width, height);
        using var g = Graphics.FromImage(bmp);

        // 绘制三层背景色，营造层次感
        g.FillRectangle(new SolidBrush(Color.FromArgb(255, 4, 181, 233)), 0, 0, bmp.Width, bmp.Height); // 整体背景（蓝色）
        g.FillRectangle(new SolidBrush(Color.FromArgb(255, 41, 146, 101)), 5, 5, bmp.Width - 12, 140); // 标题区背景（深绿色）
        g.FillRectangle(new SolidBrush(Color.FromArgb(255, 9, 236, 139)), 5, 145 + 5, bmp.Width - 12, 140 * rows + 5 * (rows + 1) + 2); // 数据区背景（浅绿色）

        // 绘制标题文字
        // 显示期号范围和游戏名称，格式：[ 起始期号 - 结束期号 ]      游戏名称
        string issue = $"[ {kjList[0].Issue} - {kjList.Last().Issue} ]      {gameName.Replace("台湾", "").Replace("宾果", "槟菓").Replace("飞艇", "")}";
        using var font = new Font("微软雅黑", 50, FontStyle.Bold);
        g.DrawString(issue, font, new SolidBrush(Color.White), 50, 25);

        // 绘制开奖号码网格
        // 采用列优先的填充方式：从左到右，每列从上到下填充
        // 这种方式符合路子图的传统显示习惯
        int index = -1;
        for (int i = 1; i <= columns; i++) // 遍历每一列
        {
            for (int j = 1; j <= rows; j++) // 遍历当前列的每一行
            {
                index++;

                // 计算当前网格位置的坐标
                int x = 149 * (i - 1) + 5 + 5 * i + 1; // X坐标：前面列的宽度 + 左边距 + 列间距 + 微调
                int y = 145 * j + 5 + 5 + 1; // Y坐标：前面行的高度 + 标题高度 + 行间距 + 微调

                // 如果数据不足，显示空图标（数字0）
                if (index >= kjList.Count)
                {
                    await DrawNumberIconAsync(g, 0, x, y);
                    continue;
                }

                // 检查当前数据是否为空数据
                if (kjList[index].DrawNum == emptyDrawNum)
                {
                    await DrawNumberIconAsync(g, 0, x, y); // 显示空图标
                    continue;
                }

                // 获取该期开奖的番摊结果并绘制对应图标（直接使用SettlementService）
                int drawResult = settlementService.CalculateFanTanResult(kjList[index].DrawNum);
                await DrawNumberIconAsync(g, drawResult, x, y);
            }
        }

        return bmp;
    }

    #endregion
}
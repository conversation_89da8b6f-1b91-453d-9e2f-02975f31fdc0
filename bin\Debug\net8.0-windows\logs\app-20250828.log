[2025-08-28 20:42:54.412 +08:00 INF] RobotService工作循环已启动
[2025-08-28 20:42:54.435 +08:00 INF] 机器人群组信息为空，开始获取群组信息
[2025-08-28 20:42:54.437 +08:00 INF] 成功获取机器人群组信息，共 1 个群组
[2025-08-28 20:42:54.438 +08:00 WRN] 当前期号为空，无法加载投注数据
[2025-08-28 20:42:54.903 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-28 20:42:54.905 +08:00 INF] === 开始投注结算处理 ===
[2025-08-28 20:42:54.936 +08:00 INF] 没有找到未结算的投注订单
[2025-08-28 20:42:54.937 +08:00 INF] 开始生成开奖数据图片
[2025-08-28 20:42:54.984 +08:00 INF] 开奖数据图片生成完成
[2025-08-28 20:42:55.012 +08:00 INF] 7行路子图生成完成
[2025-08-28 20:42:55.042 +08:00 INF] 6行路子图生成完成
[2025-08-28 20:42:55.326 +08:00 INF] 7行完整路子图生成完成
[2025-08-28 20:42:55.593 +08:00 INF] 6行完整路子图生成完成
[2025-08-28 20:42:55.593 +08:00 INF] 所有开奖图片生成完成
[2025-08-28 20:42:55.595 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-28 20:42:55.595 +08:00 INF] 游戏服务未启动，跳过发送开奖图片
[2025-08-28 20:43:02.655 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:43:03.688 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:43:04.739 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:44:19.794 +08:00 WRN] 尝试发送空消息，已忽略
[2025-08-28 20:44:20.794 +08:00 WRN] 尝试发送空消息，已忽略
[2025-08-28 20:45:52.780 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-28 20:45:52.780 +08:00 INF] === 开始投注结算处理 ===
[2025-08-28 20:45:52.781 +08:00 INF] 没有找到未结算的投注订单
[2025-08-28 20:45:52.781 +08:00 INF] 开始生成开奖数据图片
[2025-08-28 20:45:52.806 +08:00 INF] 开奖数据图片生成完成
[2025-08-28 20:45:52.825 +08:00 INF] 7行路子图生成完成
[2025-08-28 20:45:52.842 +08:00 INF] 6行路子图生成完成
[2025-08-28 20:45:53.091 +08:00 INF] 7行完整路子图生成完成
[2025-08-28 20:45:53.343 +08:00 INF] 6行完整路子图生成完成
[2025-08-28 20:45:53.343 +08:00 INF] 所有开奖图片生成完成
[2025-08-28 20:45:53.343 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-28 20:45:53.345 +08:00 INF] 系统设置 - 发送6路图: true, 发送7路图: true
[2025-08-28 20:45:53.383 +08:00 INF] 开奖数据图片发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/draw.jpeg
[2025-08-28 20:45:53.383 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:45:54.427 +08:00 INF] 6行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan6.jpeg
[2025-08-28 20:45:54.427 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:45:55.500 +08:00 INF] 7行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan7.jpeg
[2025-08-28 20:45:55.500 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:45:56.505 +08:00 INF] 开奖数据和摊路图发送完成
[2025-08-28 20:48:33.275 +08:00 INF] 已向用户发送上分申请收到反馈，账号: PWSUCVRI, 金额: 10000
[2025-08-28 20:48:33.281 +08:00 INF] 创建上分申请成功，用户: PWSUCVRI, 金额: 10000, 申请ID: 1
[2025-08-28 20:48:38.012 +08:00 INF] 已向用户发送上分申请收到反馈，账号: W9TUZ9HQ, 金额: 10000
[2025-08-28 20:48:38.014 +08:00 INF] 创建上分申请成功，用户: W9TUZ9HQ, 金额: 10000, 申请ID: 2
[2025-08-28 20:48:49.269 +08:00 INF] 创建投注订单成功，用户: PWSUCVRI, 期号: 114048683, 项目: 1正, 金额: 100, 赔率: 1.945, 订单ID: 1
[2025-08-28 20:48:49.277 +08:00 INF] 答题消息处理完成，用户: PWSUCVRI, 消息ID: 30149477-9310-4d68-b58e-4f0a152a686b, 答题项目数: 1, 投注后余额: 9900
[2025-08-28 20:48:52.066 +08:00 INF] 创建投注订单成功，用户: PWSUCVRI, 期号: 114048683, 项目: 2正, 金额: 100, 赔率: 1.945, 订单ID: 2
[2025-08-28 20:48:52.069 +08:00 INF] 答题消息处理完成，用户: PWSUCVRI, 消息ID: 0dcee8d5-5909-4ef0-8b3b-0de50b5feceb, 答题项目数: 2, 投注后余额: 9800
[2025-08-28 20:48:55.442 +08:00 INF] 创建投注订单成功，用户: PWSUCVRI, 期号: 114048683, 项目: 3正, 金额: 100, 赔率: 1.945, 订单ID: 3
[2025-08-28 20:48:55.449 +08:00 INF] 创建投注订单成功，用户: PWSUCVRI, 期号: 114048683, 项目: 4正, 金额: 100, 赔率: 1.945, 订单ID: 4
[2025-08-28 20:48:55.453 +08:00 INF] 答题消息处理完成，用户: PWSUCVRI, 消息ID: 80324107-da90-4907-a941-9062ee2dc8dc, 答题项目数: 4, 投注后余额: 9600
[2025-08-28 20:49:06.191 +08:00 WRN] 包含验证失败的指令，全部不予受理，用户: W9TUZ9HQ, 消息: 1.00 2.100 3.100 4.100, 错误详情: 1.00:投注金额必须大于0
[2025-08-28 20:49:21.320 +08:00 WRN] 当前不可答题（未开始答题或等待上期开奖），用户: W9TUZ9HQ, 期号: 114048684
[2025-08-28 20:49:21.407 +08:00 WRN] 尝试发送空消息，已忽略
[2025-08-28 20:50:47.208 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-28 20:50:47.208 +08:00 INF] === 开始投注结算处理 ===
[2025-08-28 20:50:47.208 +08:00 INF] 共找到 4 个未结算的投注订单
[2025-08-28 20:50:47.209 +08:00 INF] 涉及 1 个期号
[2025-08-28 20:50:47.209 +08:00 INF] 检查期号 114048683 - 有 4 个未结算订单
[2025-08-28 20:50:47.210 +08:00 INF] 期号 114048683 找到开奖数据 - 开奖号码: 01,04,12,18,24,31,33,34,39,45,47,54,56,64,65,66,73,74,76,78,54, 开奖时间: 2025-08-28 20:50:00
[2025-08-28 20:50:47.213 +08:00 INF] 投注结果计算完成 - 订单ID: 1, 结算状态: "Draw", 金额: 100, 番摊结果: 2
[2025-08-28 20:50:47.226 +08:00 INF] 投注订单结算成功 - 订单ID: 1, 账号: PWSUCVRI, 投注项目: 1正, 结算状态: "Draw", 金额: 100
[2025-08-28 20:50:47.226 +08:00 INF] 投注结果计算完成 - 订单ID: 2, 结算状态: "Win", 金额: 194.500, 番摊结果: 2
[2025-08-28 20:50:47.232 +08:00 INF] 投注订单结算成功 - 订单ID: 2, 账号: PWSUCVRI, 投注项目: 2正, 结算状态: "Win", 金额: 194.500
[2025-08-28 20:50:47.232 +08:00 INF] 投注结果计算完成 - 订单ID: 3, 结算状态: "Draw", 金额: 100, 番摊结果: 2
[2025-08-28 20:50:47.239 +08:00 INF] 投注订单结算成功 - 订单ID: 3, 账号: PWSUCVRI, 投注项目: 3正, 结算状态: "Draw", 金额: 100
[2025-08-28 20:50:47.239 +08:00 INF] 投注结果计算完成 - 订单ID: 4, 结算状态: "Lose", 金额: 0, 番摊结果: 2
[2025-08-28 20:50:47.241 +08:00 INF] 投注订单结算成功 - 订单ID: 4, 账号: PWSUCVRI, 投注项目: 4正, 结算状态: "Lose", 金额: 0
[2025-08-28 20:50:47.242 +08:00 INF] 期号 114048683 结算完成 - 成功: 4/4
[2025-08-28 20:50:47.242 +08:00 INF] 投注结算处理完成 - 处理期号: 1, 处理订单: 4, 成功结算: 4
[2025-08-28 20:50:47.242 +08:00 INF] 检查是否需要发送结算结果 - 成功结算订单数: 4
[2025-08-28 20:50:47.242 +08:00 INF] 开始发送结算结果到聊天平台
[2025-08-28 20:50:47.243 +08:00 INF] === 开始发送结算结果到聊天平台 ===
[2025-08-28 20:50:47.243 +08:00 INF] 获取最新开奖信息 - 期号: 114048683, 开奖号码: 01,04,12,18,24,31,33,34,39,45,47,54,56,64,65,66,73,74,76,78,54
[2025-08-28 20:50:47.243 +08:00 INF] 开始查询期号 114048683 的结算数据
[2025-08-28 20:50:47.244 +08:00 INF] 开始获取期号 114048683 的结算数据
[2025-08-28 20:50:47.245 +08:00 INF] 查询到期号 114048683 的投注记录数: 4
[2025-08-28 20:50:47.248 +08:00 INF] 查询到结算数据 - 参与会员数: 1, 总积分: 9994.5
[2025-08-28 20:50:47.248 +08:00 INF] 开始格式化结算消息
[2025-08-28 20:50:47.251 +08:00 INF] 结算消息格式化完成，消息长度: 179
[2025-08-28 20:50:47.251 +08:00 INF] 开始发送消息到聊天平台
[2025-08-28 20:50:47.254 +08:00 INF] === 结算结果发送成功 - 期号: 114048683, 参与人数: 1 ===
[2025-08-28 20:50:47.254 +08:00 INF] === 投注结算处理结束 ===
[2025-08-28 20:50:47.254 +08:00 INF] 开始生成开奖数据图片
[2025-08-28 20:50:47.278 +08:00 INF] 开奖数据图片生成完成
[2025-08-28 20:50:47.297 +08:00 INF] 7行路子图生成完成
[2025-08-28 20:50:47.314 +08:00 INF] 6行路子图生成完成
[2025-08-28 20:50:47.564 +08:00 INF] 7行完整路子图生成完成
[2025-08-28 20:50:47.819 +08:00 INF] 6行完整路子图生成完成
[2025-08-28 20:50:47.820 +08:00 INF] 所有开奖图片生成完成
[2025-08-28 20:50:47.820 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-28 20:50:47.821 +08:00 INF] 系统设置 - 发送6路图: true, 发送7路图: true
[2025-08-28 20:50:47.861 +08:00 INF] 开奖数据图片发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/draw.jpeg
[2025-08-28 20:50:47.861 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:50:48.978 +08:00 INF] 6行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan6.jpeg
[2025-08-28 20:50:48.978 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:50:50.020 +08:00 INF] 7行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan7.jpeg
[2025-08-28 20:50:50.020 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:50:51.031 +08:00 INF] 开奖数据和摊路图发送完成
[2025-08-28 20:54:20.743 +08:00 WRN] 尝试发送空消息，已忽略
[2025-08-28 20:54:21.773 +08:00 WRN] 尝试发送空消息，已忽略
[2025-08-28 20:56:40.700 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-28 20:56:40.700 +08:00 INF] === 开始投注结算处理 ===
[2025-08-28 20:56:40.700 +08:00 INF] 没有找到未结算的投注订单
[2025-08-28 20:56:40.700 +08:00 INF] 开始生成开奖数据图片
[2025-08-28 20:56:40.724 +08:00 INF] 开奖数据图片生成完成
[2025-08-28 20:56:40.743 +08:00 INF] 7行路子图生成完成
[2025-08-28 20:56:40.761 +08:00 INF] 6行路子图生成完成
[2025-08-28 20:56:41.012 +08:00 INF] 7行完整路子图生成完成
[2025-08-28 20:56:41.274 +08:00 INF] 6行完整路子图生成完成
[2025-08-28 20:56:41.274 +08:00 INF] 所有开奖图片生成完成
[2025-08-28 20:56:41.274 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-28 20:56:41.274 +08:00 INF] 系统设置 - 发送6路图: true, 发送7路图: true
[2025-08-28 20:56:41.309 +08:00 INF] 开奖数据图片发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/draw.jpeg
[2025-08-28 20:56:41.310 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:56:42.355 +08:00 INF] 6行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan6.jpeg
[2025-08-28 20:56:42.355 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:56:43.411 +08:00 INF] 7行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan7.jpeg
[2025-08-28 20:56:43.412 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 20:56:44.415 +08:00 INF] 开奖数据和摊路图发送完成
[2025-08-28 20:59:20.189 +08:00 WRN] 尝试发送空消息，已忽略
[2025-08-28 20:59:21.215 +08:00 WRN] 尝试发送空消息，已忽略
[2025-08-28 21:01:33.663 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-28 21:01:33.664 +08:00 INF] === 开始投注结算处理 ===
[2025-08-28 21:01:33.664 +08:00 INF] 没有找到未结算的投注订单
[2025-08-28 21:01:33.664 +08:00 INF] 开始生成开奖数据图片
[2025-08-28 21:01:33.688 +08:00 INF] 开奖数据图片生成完成
[2025-08-28 21:01:33.707 +08:00 INF] 7行路子图生成完成
[2025-08-28 21:01:33.730 +08:00 INF] 6行路子图生成完成
[2025-08-28 21:01:33.992 +08:00 INF] 7行完整路子图生成完成
[2025-08-28 21:01:34.251 +08:00 INF] 6行完整路子图生成完成
[2025-08-28 21:01:34.251 +08:00 INF] 所有开奖图片生成完成
[2025-08-28 21:01:34.251 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-28 21:01:34.251 +08:00 INF] 游戏服务未启动，跳过发送开奖图片
[2025-08-28 21:10:47.498 +08:00 INF] RobotService工作循环已启动
[2025-08-28 21:10:47.494 +08:00 WRN] 当前期号为空，无法加载投注数据
[2025-08-28 21:10:47.518 +08:00 INF] 机器人群组信息为空，开始获取群组信息
[2025-08-28 21:10:47.519 +08:00 INF] 成功获取机器人群组信息，共 1 个群组
[2025-08-28 21:10:47.912 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-28 21:10:47.913 +08:00 INF] === 开始投注结算处理 ===
[2025-08-28 21:10:47.915 +08:00 INF] 没有找到未结算的投注订单
[2025-08-28 21:10:47.916 +08:00 INF] 开始生成开奖数据图片
[2025-08-28 21:10:47.956 +08:00 INF] 开奖数据图片生成完成
[2025-08-28 21:10:47.982 +08:00 INF] 7行路子图生成完成
[2025-08-28 21:10:48.000 +08:00 INF] 6行路子图生成完成
[2025-08-28 21:10:48.280 +08:00 INF] 7行完整路子图生成完成
[2025-08-28 21:10:48.544 +08:00 INF] 6行完整路子图生成完成
[2025-08-28 21:10:48.544 +08:00 INF] 所有开奖图片生成完成
[2025-08-28 21:10:48.545 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-28 21:10:48.545 +08:00 INF] 游戏服务未启动，跳过发送开奖图片
[2025-08-28 21:10:50.912 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:10:51.909 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:10:52.973 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:10:54.002 +08:00 WRN] 上期开奖信息不存在，上期期号: 114048687，暂不发送开盘通知
[2025-08-28 21:11:14.532 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-28 21:11:14.532 +08:00 INF] === 开始投注结算处理 ===
[2025-08-28 21:11:14.532 +08:00 INF] 没有找到未结算的投注订单
[2025-08-28 21:11:14.532 +08:00 INF] 开始生成开奖数据图片
[2025-08-28 21:11:14.556 +08:00 INF] 开奖数据图片生成完成
[2025-08-28 21:11:14.577 +08:00 INF] 7行路子图生成完成
[2025-08-28 21:11:14.595 +08:00 INF] 6行路子图生成完成
[2025-08-28 21:11:14.858 +08:00 INF] 7行完整路子图生成完成
[2025-08-28 21:11:15.125 +08:00 INF] 6行完整路子图生成完成
[2025-08-28 21:11:15.125 +08:00 INF] 所有开奖图片生成完成
[2025-08-28 21:11:15.125 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-28 21:11:15.127 +08:00 INF] 系统设置 - 发送6路图: true, 发送7路图: true
[2025-08-28 21:11:15.188 +08:00 INF] 开奖数据图片发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/draw.jpeg
[2025-08-28 21:11:15.189 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:11:16.319 +08:00 INF] 6行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan6.jpeg
[2025-08-28 21:11:16.320 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:11:17.414 +08:00 INF] 7行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan7.jpeg
[2025-08-28 21:11:17.415 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:11:18.427 +08:00 INF] 开奖数据和摊路图发送完成
[2025-08-28 21:11:30.726 +08:00 INF] 创建投注订单成功，用户: W9TUZ9HQ, 期号: 114048688, 项目: 1正, 金额: 100, 赔率: 1.945, 订单ID: 5
[2025-08-28 21:11:30.734 +08:00 INF] 创建投注订单成功，用户: W9TUZ9HQ, 期号: 114048688, 项目: 2正, 金额: 100, 赔率: 1.945, 订单ID: 6
[2025-08-28 21:11:30.740 +08:00 INF] 创建投注订单成功，用户: W9TUZ9HQ, 期号: 114048688, 项目: 3正, 金额: 100, 赔率: 1.945, 订单ID: 7
[2025-08-28 21:11:30.747 +08:00 INF] 创建投注订单成功，用户: W9TUZ9HQ, 期号: 114048688, 项目: 4正, 金额: 100, 赔率: 1.945, 订单ID: 8
[2025-08-28 21:11:30.757 +08:00 INF] 答题消息处理完成，用户: W9TUZ9HQ, 消息ID: f45a221f-02dd-40a8-8466-d08c4e2ea20b, 答题项目数: 4, 投注后余额: 9600
[2025-08-28 21:14:20.838 +08:00 WRN] 尝试发送空消息，已忽略
[2025-08-28 21:16:10.683 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-28 21:16:10.683 +08:00 INF] === 开始投注结算处理 ===
[2025-08-28 21:16:10.684 +08:00 INF] 共找到 4 个未结算的投注订单
[2025-08-28 21:16:10.685 +08:00 INF] 涉及 1 个期号
[2025-08-28 21:16:10.685 +08:00 INF] 检查期号 114048688 - 有 4 个未结算订单
[2025-08-28 21:16:10.686 +08:00 INF] 期号 114048688 找到开奖数据 - 开奖号码: 03,09,13,18,21,24,31,36,37,40,44,45,48,49,54,64,66,69,73,76,45, 开奖时间: 2025-08-28 21:15:00
[2025-08-28 21:16:10.688 +08:00 INF] 投注结果计算完成 - 订单ID: 5, 结算状态: "Draw", 金额: 100, 番摊结果: 4
[2025-08-28 21:16:10.703 +08:00 INF] 投注订单结算成功 - 订单ID: 5, 账号: W9TUZ9HQ, 投注项目: 1正, 结算状态: "Draw", 金额: 100
[2025-08-28 21:16:10.704 +08:00 INF] 投注结果计算完成 - 订单ID: 6, 结算状态: "Lose", 金额: 0, 番摊结果: 4
[2025-08-28 21:16:10.707 +08:00 INF] 投注订单结算成功 - 订单ID: 6, 账号: W9TUZ9HQ, 投注项目: 2正, 结算状态: "Lose", 金额: 0
[2025-08-28 21:16:10.707 +08:00 INF] 投注结果计算完成 - 订单ID: 7, 结算状态: "Draw", 金额: 100, 番摊结果: 4
[2025-08-28 21:16:10.714 +08:00 INF] 投注订单结算成功 - 订单ID: 7, 账号: W9TUZ9HQ, 投注项目: 3正, 结算状态: "Draw", 金额: 100
[2025-08-28 21:16:10.714 +08:00 INF] 投注结果计算完成 - 订单ID: 8, 结算状态: "Win", 金额: 194.500, 番摊结果: 4
[2025-08-28 21:16:10.721 +08:00 INF] 投注订单结算成功 - 订单ID: 8, 账号: W9TUZ9HQ, 投注项目: 4正, 结算状态: "Win", 金额: 194.500
[2025-08-28 21:16:10.721 +08:00 INF] 期号 114048688 结算完成 - 成功: 4/4
[2025-08-28 21:16:10.721 +08:00 INF] 投注结算处理完成 - 处理期号: 1, 处理订单: 4, 成功结算: 4
[2025-08-28 21:16:10.721 +08:00 INF] 检查是否需要发送结算结果 - 成功结算订单数: 4
[2025-08-28 21:16:10.722 +08:00 INF] 开始发送结算结果到聊天平台
[2025-08-28 21:16:10.723 +08:00 INF] === 开始发送结算结果到聊天平台 ===
[2025-08-28 21:16:10.723 +08:00 INF] 获取最新开奖信息 - 期号: 114048688, 开奖号码: 03,09,13,18,21,24,31,36,37,40,44,45,48,49,54,64,66,69,73,76,45
[2025-08-28 21:16:10.723 +08:00 INF] 开始查询期号 114048688 的结算数据
[2025-08-28 21:16:10.725 +08:00 INF] 开始获取期号 114048688 的结算数据
[2025-08-28 21:16:10.725 +08:00 INF] 查询到期号 114048688 的投注记录数: 4
[2025-08-28 21:16:10.729 +08:00 INF] 查询到结算数据 - 参与会员数: 1, 总积分: 9994.5
[2025-08-28 21:16:10.729 +08:00 INF] 开始格式化结算消息
[2025-08-28 21:16:10.732 +08:00 INF] 结算消息格式化完成，消息长度: 167
[2025-08-28 21:16:10.732 +08:00 INF] 开始发送消息到聊天平台
[2025-08-28 21:16:10.735 +08:00 INF] === 结算结果发送成功 - 期号: 114048688, 参与人数: 1 ===
[2025-08-28 21:16:10.735 +08:00 INF] === 投注结算处理结束 ===
[2025-08-28 21:16:10.735 +08:00 INF] 开始生成开奖数据图片
[2025-08-28 21:16:10.759 +08:00 INF] 开奖数据图片生成完成
[2025-08-28 21:16:10.778 +08:00 INF] 7行路子图生成完成
[2025-08-28 21:16:10.796 +08:00 INF] 6行路子图生成完成
[2025-08-28 21:16:11.062 +08:00 INF] 7行完整路子图生成完成
[2025-08-28 21:16:11.330 +08:00 INF] 6行完整路子图生成完成
[2025-08-28 21:16:11.330 +08:00 INF] 所有开奖图片生成完成
[2025-08-28 21:16:11.330 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-28 21:16:11.331 +08:00 INF] 系统设置 - 发送6路图: true, 发送7路图: true
[2025-08-28 21:16:11.369 +08:00 INF] 开奖数据图片发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/draw.jpeg
[2025-08-28 21:16:11.369 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:16:12.431 +08:00 INF] 6行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan6.jpeg
[2025-08-28 21:16:12.432 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:16:13.509 +08:00 INF] 7行摊路图发送成功: F:\RobotCopy\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/images/tan7.jpeg
[2025-08-28 21:16:13.510 +08:00 WRN] 消息转换结果为空，平台: OneChat
[2025-08-28 21:16:14.511 +08:00 INF] 开奖数据和摊路图发送完成

namespace CommandGuard.Interfaces.Infrastructure;

/// <summary>
/// 系统管理服务接口
/// </summary>
public interface ISystemManagementService
{
    /// <summary>
    /// 清除一切记录
    /// 删除所有业务数据，但保留用户自定义的系统参数设置
    /// </summary>
    /// <param name="operatorName">操作人员名称</param>
    /// <returns>是否清除成功</returns>
    Task<bool> ClearAllRecordsAsync(string operatorName);

    /// <summary>
    /// 初始化出厂设置
    /// 删除所有业务数据，并重置所有系统参数为出厂默认值
    /// </summary>
    /// <param name="operatorName">操作人员名称</param>
    /// <returns>是否初始化成功</returns>
    Task<bool> InitializeFactorySettingsAsync(string operatorName);


}

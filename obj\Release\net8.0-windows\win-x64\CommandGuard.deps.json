{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"CommandGuard/1.0.0": {"dependencies": {"Autoupdater.NET.Official": "1.9.2", "CommandGuard.Licensing": "1.0.0", "Costura.Fody": "6.0.0", "Flurl.Http": "4.0.2", "FreeSql": "3.5.212", "FreeSql.Provider.Sqlite": "3.5.212", "Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Json": "9.0.8", "Microsoft.Extensions.DependencyInjection": "9.0.8", "Newtonsoft.Json": "13.0.3", "Serilog": "4.3.0", "Serilog.Extensions.Logging": "9.0.2", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "7.0.0", "AiHelper": "*******", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.17", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "8.0.17"}, "runtime": {"CommandGuard.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.17": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1725.26602"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.1725.26602"}, "clretwrc.dll": {"fileVersion": "8.0.1725.26602"}, "clrgc.dll": {"fileVersion": "8.0.1725.26602"}, "clrjit.dll": {"fileVersion": "8.0.1725.26602"}, "coreclr.dll": {"fileVersion": "8.0.1725.26602"}, "createdump.exe": {"fileVersion": "8.0.1725.26602"}, "hostfxr.dll": {"fileVersion": "8.0.1725.26602"}, "hostpolicy.dll": {"fileVersion": "8.0.1725.26602"}, "mscordaccore.dll": {"fileVersion": "8.0.1725.26602"}, "mscordaccore_amd64_amd64_8.0.1725.26602.dll": {"fileVersion": "8.0.1725.26602"}, "mscordbi.dll": {"fileVersion": "8.0.1725.26602"}, "mscorrc.dll": {"fileVersion": "8.0.1725.26602"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.17": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1725.26604"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26602"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26604"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1725.26609"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "8.0.1725.26609"}, "PresentationNative_cor3.dll": {"fileVersion": "8.0.25.16802"}, "vcruntime140_cor3.dll": {"fileVersion": "14.44.34918.1"}, "wpfgfx_cor3.dll": {"fileVersion": "8.0.1725.26609"}}}, "Autoupdater.NET.Official/1.9.2": {"dependencies": {"Microsoft.Web.WebView2": "1.0.2592.51"}, "runtime": {"lib/net8.0-windows7.0/AutoUpdater.NET.dll": {"assemblyVersion": "1.9.2.0", "fileVersion": "1.9.2.0"}}}, "Costura.Fody/6.0.0": {"dependencies": {"Fody": "6.8.2"}}, "Flurl/4.0.0": {"runtime": {"lib/netstandard2.0/Flurl.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Flurl.Http/4.0.2": {"dependencies": {"Flurl": "4.0.0"}, "runtime": {"lib/net6.0/Flurl.Http.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}}, "Fody/6.8.2": {}, "FreeSql/3.5.212": {"runtime": {"lib/netstandard2.1/FreeSql.dll": {"assemblyVersion": "3.5.212.0", "fileVersion": "3.5.212.0"}}}, "FreeSql.Provider.Sqlite/3.5.212": {"dependencies": {"FreeSql": "3.5.212", "System.Data.SQLite.Core": "1.0.119"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"assemblyVersion": "3.5.212.0", "fileVersion": "3.5.212.0"}}}, "Microsoft.Extensions.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"dependencies": {"System.Text.Encodings.Web": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileSystemGlobbing": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Web.WebView2/1.0.2592.51": {"runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}}, "native": {"runtimes/win-x64/native/WebView2Loader.dll": {"fileVersion": "1.0.2592.51"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Serilog/4.3.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "4.3.0.0", "fileVersion": "4.3.0.0"}}}, "Serilog.Extensions.Logging/9.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "9.0.2.0", "fileVersion": "9.0.2.0"}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.File/7.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.0.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "native": {"runtimes/win-x64/native/SQLite.Interop.dll": {"fileVersion": "*********"}}}, "System.CodeDom/8.0.0": {}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.Diagnostics.DiagnosticSource/9.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.IO.Pipelines/9.0.8": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Management/8.0.0": {"dependencies": {"System.CodeDom": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Encodings.Web/9.0.8": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Text.Json/9.0.8": {"dependencies": {"System.IO.Pipelines": "9.0.8", "System.Text.Encodings.Web": "9.0.8"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "CommandGuard.Licensing/1.0.0": {"dependencies": {"System.Management": "8.0.0"}, "runtime": {"CommandGuard.Licensing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AiHelper/*******": {"runtime": {"AiHelper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"CommandGuard/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.17": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.17": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Autoupdater.NET.Official/1.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-wVB0YMk5Dyc2UMUb46t5TVvzipfgkNHtuKh04wp14k/32nY7wMTz8gSMgiaX9WAHesW60K6J8uGz5G8gyGkEPQ==", "path": "autoupdater.net.official/1.9.2", "hashPath": "autoupdater.net.official.1.9.2.nupkg.sha512"}, "Costura.Fody/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Uriu9GJABMivG0wXMJs6NQ7FNE3pylir1gZEBAWDvpii3cnrmxXnOG44MMDuIVOIk/Xhef7WZFsaCNV+py9qA==", "path": "costura.fody/6.0.0", "hashPath": "costura.fody.6.0.0.nupkg.sha512"}, "Flurl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rpts69yYgvJqg6PPgqShBQEZ4aNzWQqWpWppcT0oDWxDCIsBqiod4pj6LQZdhk+1OozLFagemldMRACdHF3CsA==", "path": "flurl/4.0.0", "hashPath": "flurl.4.0.0.nupkg.sha512"}, "Flurl.Http/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9vCqFFyceA11yplkFD8AbCFFTvG1Lrw3tpsgOpL5sLUc28p6zcvGszNleuT6nDymRvtt5eS+rqUX+bRztg1fhA==", "path": "flurl.http/4.0.2", "hashPath": "flurl.http.4.0.2.nupkg.sha512"}, "Fody/6.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-sjGHrtGS1+kcrv99WXCvujOFBTQp4zCH3ZC9wo2LAtVaJkuLpHghQx3y4k1Q8ZKuDAbEw+HE6ZjPUJQK3ejepQ==", "path": "fody/6.8.2", "hashPath": "fody.6.8.2.nupkg.sha512"}, "FreeSql/3.5.212": {"type": "package", "serviceable": true, "sha512": "sha512-8QJlbW0khtPKzWGzXonp5I8mEPrPVrouYCdFWr3cAtAUjlnj+qb0JYMj3rPFN55ZN4cvP2eu2j1LiFLERqQNZQ==", "path": "freesql/3.5.212", "hashPath": "freesql.3.5.212.nupkg.sha512"}, "FreeSql.Provider.Sqlite/3.5.212": {"type": "package", "serviceable": true, "sha512": "sha512-wAJ0CefIhLizdCtCnxGi0U20Qvm09L57+A/ogdsgFWBSEusGUVUJnGm2eXOu0SuEplEwWH+da4a7dUYde8if4A==", "path": "freesql.provider.sqlite/3.5.212", "hashPath": "freesql.provider.sqlite.3.5.212.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6m+8Xgmf8UWL0p/oGqBM+0KbHE5/ePXbV1hKXgC59zEv0aa0DW5oiiyxDbK5kH5j4gIvyD5uWL0+HadKBJngvQ==", "path": "microsoft.extensions.configuration/9.0.8", "hashPath": "microsoft.extensions.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNou2KM35RvzOh4vUFtl2l33rWPvOCoba+nzEDJ+BgD8aOL/jew4WPCibQvntRfOJ2pJU8ARygSMD+pdjvDHuA==", "path": "microsoft.extensions.configuration.abstractions/9.0.8", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2jgx58Jpk3oKT7KRn8x/cFf3QDTjQP+KUbyBnynAcB2iBx1Eq9EdNMCu0QEbYuaZOaQru/Kwdffary+hn58Wwg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.8", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-vjxzcnL7ul322+kpvELisXaZl8/5MYs6JfI9DZLQWsao1nA/4FL48yPwDK986hbJTWc64JxOOaMym0SQ/dy32w==", "path": "microsoft.extensions.configuration.json/9.0.8", "hashPath": "microsoft.extensions.configuration.json.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "path": "microsoft.extensions.dependencyinjection/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4zZbQ4w+hCMm9J+z5NOj3giIPT2MhZxx05HX/MGuAmDBbjOuXlYIIRN+t4V6OLxy5nXZIcXO+dQMB/OWubuDkw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.8", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-FlOe2i7UUIfY0l0ChaIYtlXjdWWutR4DMRKZaGD6z4G1uVTteFkbBfxUIoi1uGmrZQxXe/yv/cfwiT0tK2xyXA==", "path": "microsoft.extensions.fileproviders.physical/9.0.8", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-96Ub5LmwYfIGVoXkbe4kjs+ivK6fLBTwKJAOMfUNV0R+AkZRItlgROFqXEWMUlXBTPM1/kKu26Ueu5As6RDzJA==", "path": "microsoft.extensions.filesystemglobbing/9.0.8", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.2592.51": {"type": "package", "serviceable": true, "sha512": "sha512-AC9aWCthS2JvddYA1jl4dFpLBW3GsLRInhp5dkcBzaFXsRehfoUN9olIUsrH41eNaNYd7z9NRvmy81aUA5aD1g==", "path": "microsoft.web.webview2/1.0.2592.51", "hashPath": "microsoft.web.webview2.1.0.2592.51.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Serilog/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "path": "serilog/4.3.0", "hashPath": "serilog.4.3.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-p8kk2McN6LxuQfLyCoOkL7+nJIhVKnV1WFUxAaGTQTQk0wySbgmCHe98j+xSQvIbYHtzKXROOE2G2R0TLwBfig==", "path": "serilog.extensions.logging/9.0.2", "hashPath": "serilog.extensions.logging.9.0.2.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "path": "serilog.settings.configuration/9.0.0", "hashPath": "serilog.settings.configuration.9.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "path": "serilog.sinks.file/7.0.0", "hashPath": "serilog.sinks.file.7.0.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "System.CodeDom/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "path": "system.codedom/8.0.0", "hashPath": "system.codedom.8.0.0.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "path": "system.diagnostics.diagnosticsource/9.0.0", "hashPath": "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6vPmJt73mgUo1gzc/OcXlJvClz/2jxZ4TQPRfriVaLoGRH2mye530D9WHJYbFQRNMxF3PWCoeofsFdCyN7fLzA==", "path": "system.io.pipelines/9.0.8", "hashPath": "system.io.pipelines.9.0.8.nupkg.sha512"}, "System.Management/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "path": "system.management/8.0.0", "hashPath": "system.management.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-W+LotQsM4wBJ4PG7uRkyul4wqL4Fz7R4ty6uXrCNZUhbaHYANgrPaYR2ZpMVpdCjQEJ17Jr1NMN8hv4SHaHY4A==", "path": "system.text.encodings.web/9.0.8", "hashPath": "system.text.encodings.web.9.0.8.nupkg.sha512"}, "System.Text.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-mIQir9jBqk0V7X0Nw5hzPJZC8DuGdf+2DS3jAVsr6rq5+/VyH5rza0XGcONJUWBrZ+G6BCwNyjWYd9lncBu48A==", "path": "system.text.json/9.0.8", "hashPath": "system.text.json.9.0.8.nupkg.sha512"}, "CommandGuard.Licensing/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AiHelper/*******": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x64": ["win", "any", "base"]}}
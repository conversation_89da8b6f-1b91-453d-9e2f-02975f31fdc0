namespace CommandGuard.Helpers
{
    /// <summary>
    /// 彩票开奖结果数据模型
    /// </summary>
    public class LotteryResult
    {
        #region 属性定义

        /// <summary>
        /// 期号
        /// </summary>
        public string PeriodNumber { get; set; }

        /// <summary>
        /// 超级号码
        /// </summary>
        public int Number { get; set; }

        /// <summary>
        /// 番摊数值 (超级号码除以4的余数，0则为4)
        /// </summary>
        public int FanTan { get; private set; }

        /// <summary>
        /// 达晓 (1,2=小, 3,4=大)
        /// </summary>
        public string BigSmall { get; private set; } = string.Empty;

        /// <summary>
        /// 单双 (1,3=单, 2,4=双)
        /// </summary>
        public string SingleDouble { get; private set; } = string.Empty;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数 - 只需传入期号和超级号码，其他自动计算
        /// </summary>
        public LotteryResult(string periodNumber, int number)
        {
            PeriodNumber = periodNumber;
            Number = number;
            CalculateAll();
        }

        #endregion

        #region 计算方法

        /// <summary>
        /// 自动计算所有衍生数据
        /// </summary>
        private void CalculateAll()
        {
            // 计算番摊数值：超级号码除以4的余数，余数为0则为4
            FanTan = Number % 4;
            if (FanTan == 0) FanTan = 4;

            // 计算达晓：1,2为小，3,4为大
            BigSmall = FanTan <= 2 ? @"小" : @"大";

            // 计算单双：1,3为单，2,4为双
            SingleDouble = (FanTan == 1 || FanTan == 3) ? @"單" : @"雙";
        }

        #endregion
    }
}
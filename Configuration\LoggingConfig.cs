namespace CommandGuard.Configuration;

/// <summary>
/// 日志配置根类
/// 用于映射 logging.json 配置文件的结构
/// 包含Microsoft.Extensions.Logging和Serilog的配置
/// </summary>
public class LoggingConfig
{
    /// <summary>
    /// Microsoft.Extensions.Logging配置节
    /// 标准的.NET日志配置
    /// </summary>
    public Logging Logging { get; set; } = new();

    /// <summary>
    /// Serilog日志配置节
    /// 结构化日志配置，支持多种输出目标
    /// </summary>
    public Serilog Serilog { get; set; } = new();
}

/// <summary>
/// Microsoft.Extensions.Logging配置类
/// 标准的.NET日志级别配置
/// </summary>
public class Logging
{
    /// <summary>
    /// 日志级别配置
    /// 控制不同命名空间的日志输出级别
    /// </summary>
    public LogLevel LogLevel { get; set; } = new();
}

/// <summary>
/// 日志级别配置类
/// 定义不同组件的日志输出级别
/// </summary>
public class LogLevel
{
    /// <summary>
    /// 默认日志级别
    /// 可选值：Trace, Debug, Information, Warning, Error, Critical, None
    /// </summary>
    public string Default { get; set; } = "Information";

    /// <summary>
    /// Microsoft组件的日志级别
    /// 通常设置为Warning以减少框架内部日志
    /// </summary>
    public string Microsoft { get; set; } = "Warning";

    /// <summary>
    /// Microsoft.Hosting.Lifetime的日志级别
    /// 控制应用程序生命周期相关的日志
    /// </summary>
    public string MicrosoftHostingLifetime { get; set; } = "Information";
}

/// <summary>
/// Serilog配置类
/// 结构化日志配置，支持丰富的输出格式和目标
/// </summary>
public class Serilog
{
    /// <summary>
    /// 最小日志级别配置
    /// 控制全局和特定命名空间的日志级别
    /// </summary>
    public MinimumLevel MinimumLevel { get; set; } = new();

    /// <summary>
    /// 日志输出目标配置列表
    /// 支持控制台、文件、数据库等多种输出方式
    /// </summary>
    public List<WriteTo> WriteTo { get; set; } = [];
}

/// <summary>
/// Serilog最小日志级别配置类
/// 控制日志的最小输出级别
/// </summary>
public class MinimumLevel
{
    /// <summary>
    /// 默认最小日志级别
    /// 可选值：Verbose, Debug, Information, Warning, Error, Fatal
    /// </summary>
    public string Default { get; set; } = "Information";

    /// <summary>
    /// 特定命名空间的日志级别覆盖
    /// 键为命名空间，值为日志级别
    /// </summary>
    public Dictionary<string, string> Override { get; set; } = new();
}

/// <summary>
/// Serilog输出目标配置类
/// 定义日志的输出方式和格式
/// </summary>
public class WriteTo
{
    /// <summary>
    /// 输出目标名称
    /// 如：Console, File, Seq, ElasticSearch等
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 输出目标的参数配置
    /// 不同的输出目标有不同的参数
    /// </summary>
    public Dictionary<string, object> Args { get; set; } = new();
}

namespace CommandGuard.Enums;

/// <summary>
/// 飞单状态枚举
/// 定义投注数据抛售到平台的状态
/// </summary>
public enum EnumFlightOrderStatus
{
    /// <summary>
    /// 待飞单 - 投注数据等待抛售到平台
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 飞单中 - 投注数据正在抛售到平台
    /// </summary>
    Processing = 1,

    /// <summary>
    /// 飞单成功 - 投注数据成功抛售到平台
    /// </summary>
    Success = 2,

    /// <summary>
    /// 飞单失败 - 投注数据抛售到平台失败
    /// </summary>
    Failed = 3
}

/// <summary>
/// 飞单状态扩展方法
/// </summary>
public static class FlightOrderStatusExtensions
{
    /// <summary>
    /// 获取状态的中文描述
    /// </summary>
    /// <param name="status">飞单状态</param>
    /// <returns>中文描述</returns>
    public static string GetDescription(this EnumFlightOrderStatus status)
    {
        return status switch
        {
            EnumFlightOrderStatus.Pending => @"待飞单",
            EnumFlightOrderStatus.Processing => @"飞单中",
            EnumFlightOrderStatus.Success => @"飞单成功",
            EnumFlightOrderStatus.Failed => @"飞单失败",
            _ => @"未知状态"
        };
    }

    /// <summary>
    /// 获取状态的颜色代码（用于UI显示）
    /// </summary>
    /// <param name="status">飞单状态</param>
    /// <returns>颜色代码</returns>
    public static string GetColorCode(this EnumFlightOrderStatus status)
    {
        return status switch
        {
            EnumFlightOrderStatus.Pending => @"Orange",
            EnumFlightOrderStatus.Processing => @"Blue",
            EnumFlightOrderStatus.Success => @"Green",
            EnumFlightOrderStatus.Failed => @"Red",
            _ => @"Gray"
        };
    }

    /// <summary>
    /// 判断是否为最终状态（不会再变更的状态）
    /// </summary>
    /// <param name="status">飞单状态</param>
    /// <returns>是否为最终状态</returns>
    public static bool IsFinalStatus(this EnumFlightOrderStatus status)
    {
        return status is EnumFlightOrderStatus.Success or EnumFlightOrderStatus.Failed;
    }

    /// <summary>
    /// 判断是否为成功状态
    /// </summary>
    /// <param name="status">飞单状态</param>
    /// <returns>是否为成功状态</returns>
    public static bool IsSuccessStatus(this EnumFlightOrderStatus status)
    {
        return status == EnumFlightOrderStatus.Success;
    }

    /// <summary>
    /// 判断是否可以重试
    /// </summary>
    /// <param name="status">飞单状态</param>
    /// <returns>是否可以重试</returns>
    public static bool CanRetry(this EnumFlightOrderStatus status)
    {
        return status == EnumFlightOrderStatus.Failed;
    }
}
using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 财务记录模型
/// 记录所有余额变更的明细信息
/// </summary>
[Table(Name = @"FinancialRecords")]
[Index(@"idx_financial_account", nameof(Account))]
[Index(@"idx_financial_type", nameof(Type))]
[Index(@"idx_financial_created", nameof(CreatedTime))]
[Index(@"idx_financial_reference", @"ReferenceType,ReferenceId")]
public class FinancialRecord
{
    /// <summary>
    /// 记录ID，自增主键
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 用户账号
    /// </summary>
    [Required(ErrorMessage = @"用户账号不能为空")]
    [StringLength(50, ErrorMessage = @"用户账号长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 交易类型
    /// </summary>
    public EnumFinancialType Type { get; set; }

    /// <summary>
    /// 变更金额（正数为增加，负数为减少）
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 变更前余额
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal BalanceBefore { get; set; }

    /// <summary>
    /// 变更后余额
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal BalanceAfter { get; set; }

    /// <summary>
    /// 关联类型（如：DepositRequest、WithdrawRequest）
    /// </summary>
    [StringLength(50, ErrorMessage = @"关联类型长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string ReferenceType { get; set; } = string.Empty;

    /// <summary>
    /// 关联ID（如：申请订单的ID）
    /// </summary>
    public long ReferenceId { get; set; }

    /// <summary>
    /// 操作描述
    /// </summary>
    [StringLength(500, ErrorMessage = @"操作描述长度不能超过500个字符")]
    [Column(StringLength = 500)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 操作人员
    /// </summary>
    [StringLength(50, ErrorMessage = @"操作人员长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string Operator { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column(ServerTime = DateTimeKind.Local)]
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 备注信息
    /// </summary>
    [StringLength(500, ErrorMessage = @"备注信息长度不能超过500个字符")]
    [Column(StringLength = 500)]
    public string Note { get; set; } = string.Empty;
}

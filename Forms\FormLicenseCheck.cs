using CommandGuard.Licensing.Models;
using CommandGuard.Licensing.Services;
using System.Runtime.InteropServices;

namespace CommandGuard.Forms;

/// <summary>
/// 授权验证窗体
/// 在程序启动时验证授权文件
/// </summary>
public partial class FormLicenseCheck : Form
{
    #region Windows API 声明

    [DllImport("user32.dll")]
    private static extern bool OpenClipboard(IntPtr hWndNewOwner);

    [DllImport("user32.dll")]
    private static extern bool CloseClipboard();

    [DllImport("user32.dll")]
    private static extern bool EmptyClipboard();

    [DllImport("user32.dll")]
    private static extern IntPtr SetClipboardData(uint uFormat, IntPtr hMem);

    [DllImport("kernel32.dll")]
    private static extern IntPtr GlobalAlloc(uint uFlags, UIntPtr dwBytes);

    [DllImport("kernel32.dll")]
    private static extern IntPtr GlobalLock(IntPtr hMem);

    [DllImport("kernel32.dll")]
    private static extern bool GlobalUnlock(IntPtr hMem);

    [DllImport("kernel32.dll")]
    private static extern IntPtr GlobalFree(IntPtr hMem);

    private const uint CF_TEXT = 1;
    private const uint CF_UNICODETEXT = 13;
    private const uint GMEM_MOVEABLE = 0x0002;

    #endregion

    private LicenseInfo? _licenseInfo;
    private bool _isLicenseValid;

    public FormLicenseCheck()
    {
        InitializeComponent();
        InitializeForm();

        // 启用键盘事件处理
        KeyPreview = true;
    }

    /// <summary>
    /// 初始化窗体
    /// </summary>
    private void InitializeForm()
    {
        // 显示当前机器码
        var machineCode = MachineCodeService.GenerateMachineCode();
        textBox_MachineCode.Text = machineCode;

        // 设置窗体标题显示机器码
        Text = $@"指令卫士 - 授权验证 (机器码: {machineCode})";

        // 检查授权文件
        CheckLicenseFile();
    }

    /// <summary>
    /// 检查授权文件
    /// </summary>
    private void CheckLicenseFile()
    {
        try
        {
            var licenseFilePath = Path.Combine(Application.StartupPath, LicenseService.LicenseFileName);
            var result = LicenseService.ValidateLicenseFile(licenseFilePath);

            if (result.IsValid && result.LicenseInfo != null)
            {
                _licenseInfo = result.LicenseInfo;
                _isLicenseValid = true;

                // 显示授权有效信息
                label_Status.Text = "授权验证通过";
                label_Status.ForeColor = Color.Green;

                var licenseText = $"授权用户：{_licenseInfo.LicensedTo}\n" +
                                 $"授权类型：{GetLicenseTypeText(_licenseInfo.LicenseType)}\n" +
                                 $"产品版本：{_licenseInfo.ProductVersion}\n" +
                                 $"最大用户：{_licenseInfo.MaxUsers}\n" +
                                 $"到期时间：{(_licenseInfo.IsPermanent ? "永久" : _licenseInfo.ExpiresAt.ToString("yyyy-MM-dd"))}\n" +
                                 $"剩余天数：{(_licenseInfo.IsPermanent ? "永久" : _licenseInfo.GetRemainingDays().ToString())}";

                label_LicenseInfo.Text = licenseText;

                // 授权验证通过，延迟2秒后自动关闭窗口
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 2000; // 2秒
                timer.Tick += (s, e) =>
                {
                    timer.Stop();
                    timer.Dispose();
                    DialogResult = DialogResult.OK;
                    Close();
                };
                timer.Start();

            }
            else
            {
                _isLicenseValid = false;

                // 显示授权无效信息
                label_Status.Text = "授权验证失败";
                label_Status.ForeColor = Color.Red;

                var errorText = string.IsNullOrEmpty(result.ErrorMessage)
                    ? "未找到有效的授权文件"
                    : result.ErrorMessage;

                var helpText = GetHelpTextForError(result.ErrorMessage);

                label_LicenseInfo.Text = $"错误信息：{errorText}\n\n{helpText}\n\n" +
                                        "请联系软件供应商获取授权文件，或点击下方按钮选择授权文件。";


            }
        }
        catch (Exception ex)
        {
            _isLicenseValid = false;
            label_Status.Text = "授权检查异常";
            label_Status.ForeColor = Color.Red;
            label_LicenseInfo.Text = $"检查授权时发生异常：{ex.Message}";

        }
    }

    /// <summary>
    /// 复制机器码按钮点击事件
    /// </summary>
    private void button_CopyMachineCode_Click(object sender, EventArgs e)
    {
        try
        {
            var machineCode = textBox_MachineCode.Text;

            if (string.IsNullOrEmpty(machineCode))
            {
                MessageBox.Show("机器码为空，无法复制", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 确保在UI线程中执行剪贴板操作
            if (InvokeRequired)
            {
                Invoke(new Action(() => CopyToClipboardSafe(machineCode)));
            }
            else
            {
                CopyToClipboardSafe(machineCode);
            }
        }
        catch (Exception ex)
        {
            ShowCopyError(ex.Message);
        }
    }

    /// <summary>
    /// 安全的剪贴板复制操作（使用Windows API）
    /// </summary>
    private void CopyToClipboardSafe(string text)
    {
        try
        {
            if (SetClipboardTextUsingAPI(text))
            {
                MessageBox.Show("机器码已复制到剪贴板", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                ShowCopyError("Windows API复制失败");
            }
        }
        catch (Exception ex)
        {
            ShowCopyError(ex.Message);
        }
    }

    /// <summary>
    /// 使用Windows API设置剪贴板文本
    /// </summary>
    private bool SetClipboardTextUsingAPI(string text)
    {
        try
        {
            if (!OpenClipboard(Handle))
                return false;

            EmptyClipboard();

            // 转换为字节数组
            var bytes = System.Text.Encoding.Unicode.GetBytes(text + '\0');

            // 分配全局内存
            var hGlobal = GlobalAlloc(GMEM_MOVEABLE, (UIntPtr)bytes.Length);
            if (hGlobal == IntPtr.Zero)
            {
                CloseClipboard();
                return false;
            }

            // 锁定内存并复制数据
            var lpMem = GlobalLock(hGlobal);
            if (lpMem != IntPtr.Zero)
            {
                Marshal.Copy(bytes, 0, lpMem, bytes.Length);
                GlobalUnlock(hGlobal);

                // 设置剪贴板数据
                if (SetClipboardData(CF_UNICODETEXT, hGlobal) != IntPtr.Zero)
                {
                    CloseClipboard();
                    return true;
                }
            }

            GlobalFree(hGlobal);
            CloseClipboard();
            return false;
        }
        catch
        {
            try { CloseClipboard(); } catch { }
            return false;
        }
    }

    /// <summary>
    /// 显示复制错误并提供备选方案
    /// </summary>
    private void ShowCopyError(string errorMessage)
    {
        MessageBox.Show($"自动复制失败：{errorMessage}\n\n" +
                       "请手动复制机器码：\n" +
                       "1. 机器码文本已自动选中\n" +
                       "2. 按 Ctrl+C 复制到剪贴板\n" +
                       "3. 或右键选择复制",
                       "复制失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);

        // 自动选中文本方便用户手动复制
        try
        {
            textBox_MachineCode.SelectAll();
            textBox_MachineCode.Focus();
        }
        catch
        {
            // 忽略选中文本时的异常
        }
    }



    /// <summary>
    /// 获取授权类型文本
    /// </summary>
    private static string GetLicenseTypeText(LicenseType licenseType)
    {
        return licenseType switch
        {
            LicenseType.Trial => "试用版",
            LicenseType.Standard => "标准版",
            LicenseType.Professional => "专业版",
            LicenseType.Enterprise => "企业版",
            _ => "未知"
        };
    }

    /// <summary>
    /// 获取当前授权信息（供外部调用）
    /// </summary>
    public LicenseInfo? GetLicenseInfo()
    {
        return _licenseInfo;
    }

    /// <summary>
    /// 检查授权是否有效（供外部调用）
    /// </summary>
    public bool IsLicenseValid()
    {
        return _isLicenseValid;
    }

    /// <summary>
    /// 根据错误信息获取帮助文本
    /// </summary>
    private static string GetHelpTextForError(string errorMessage)
    {
        return errorMessage switch
        {
            var msg when msg.Contains("授权文件不存在") =>
                "解决方案：\n" +
                "1. 确认授权文件 指令卫士.lic 已放置在程序所在目录\n" +
                "2. 检查文件名是否正确（区分大小写）",

            var msg when msg.Contains("机器码不匹配") =>
                "解决方案：\n" +
                "1. 此授权文件是为其他机器生成的\n" +
                "2. 请使用当前机器码重新申请授权文件\n" +
                "3. 复制上方机器码发送给软件供应商",

            var msg when msg.Contains("授权已过期") =>
                "解决方案：\n" +
                "1. 授权已超过有效期\n" +
                "2. 请联系软件供应商续期或获取新的授权文件",

            var msg when msg.Contains("签名验证失败") =>
                "解决方案：\n" +
                "1. 授权文件可能已损坏或被篡改\n" +
                "2. 请重新获取原始授权文件\n" +
                "3. 确保文件传输过程中未被修改",

            var msg when msg.Contains("格式错误") =>
                "解决方案：\n" +
                "1. 授权文件格式不正确\n" +
                "2. 请确认使用的是正确的授权文件\n" +
                "3. 重新下载或获取授权文件",

            _ =>
                "解决方案：\n" +
                "1. 检查授权文件是否存在且完整\n" +
                "2. 确认机器码是否匹配\n" +
                "3. 联系技术支持获取帮助"
        };
    }

    /// <summary>
    /// 处理键盘快捷键
    /// </summary>
    protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
    {
        // 处理 Ctrl+C 复制机器码
        if (keyData == (Keys.Control | Keys.C))
        {
            // 如果机器码文本框有焦点，则复制机器码
            if (textBox_MachineCode.Focused || ActiveControl == textBox_MachineCode)
            {
                button_CopyMachineCode_Click(this, EventArgs.Empty);
                return true;
            }
        }

        // 处理 F5 刷新授权检查
        if (keyData == Keys.F5)
        {
            CheckLicenseFile();
            return true;
        }

        // 处理 Escape 退出
        if (keyData == Keys.Escape)
        {
            DialogResult = DialogResult.Cancel;
            Close();
            return true;
        }

        return base.ProcessCmdKey(ref msg, keyData);
    }
}

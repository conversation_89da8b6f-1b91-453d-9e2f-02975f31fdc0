using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 输赢流水
/// </summary>
public partial class FormMain
{
    #region 输赢流水回水记录查询界面事件处理

    /// <summary>
    /// 输赢流水回水记录查询按钮点击事件处理
    /// </summary>
    private async void button_根据条件查询输赢流水回水记录_Click(object sender, EventArgs e)
    {
        try
        {
            // 禁用查询按钮，防止重复点击
            button_根据条件查询输赢流水回水记录.Enabled = false;
            button_根据条件查询输赢流水回水记录.Text = @"查询中...";

            // 获取查询条件
            var queryConditions = GetWinLossRebateQueryConditions();

            // 验证查询条件
            if (!ValidateWinLossRebateQueryConditions(queryConditions))
            {
                return;
            }

            // 执行查询
            await QueryWinLossRebateRecordsAsync(
                queryConditions.StartTime,
                queryConditions.EndTime,
                queryConditions.Account,
                queryConditions.Issue,
                queryConditions.IncludeFakeUsers
            );

            _logger.LogInformation(@"输赢流水回水记录查询完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询输赢流水回水记录时发生错误");
            MessageBox.Show($@"查询失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 恢复查询按钮状态
            button_根据条件查询输赢流水回水记录.Enabled = true;
            button_根据条件查询输赢流水回水记录.Text = @"查询";
        }
    }

    /// <summary>
    /// 获取输赢流水回水记录查询条件
    /// </summary>
    private WinLossRebateQueryConditions GetWinLossRebateQueryConditions()
    {
        try
        {
            // 获取开始时间
            var startDate = dateTimePicker_查询输赢流水回水记录开始日期.Value.Date;
            var startHour = (int)numericUpDown_查询输赢流水回水记录开始小时.Value;
            var startMinute = (int)numericUpDown_查询输赢流水回水记录开始分钟.Value;
            var startTime = startDate.AddHours(startHour).AddMinutes(startMinute);

            // 获取结束时间
            var endDate = dateTimePicker_查询输赢流水回水记录结束日期.Value.Date;
            var endHour = (int)numericUpDown_查询输赢流水回水记录结束小时.Value;
            var endMinute = (int)numericUpDown_查询输赢流水回水记录结束分钟.Value;
            var endTime = endDate.AddHours(endHour).AddMinutes(endMinute);

            // 获取其他条件
            var account = textBox_查询输赢流水回水记录账号.Text.Trim();
            var issue = textBox_查询输赢流水回水记录期号.Text.Trim();
            var includeFakeUsers = checkBox_查询输赢流水回水记录包含假人.Checked;

            return new WinLossRebateQueryConditions
            {
                StartTime = startTime,
                EndTime = endTime,
                Account = string.IsNullOrEmpty(account) ? null : account,
                Issue = string.IsNullOrEmpty(issue) ? null : issue,
                IncludeFakeUsers = includeFakeUsers
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"获取输赢流水回水记录查询条件时发生错误");
            throw new InvalidOperationException(@"获取查询条件失败", ex);
        }
    }

    /// <summary>
    /// 验证输赢流水回水记录查询条件
    /// </summary>
    private bool ValidateWinLossRebateQueryConditions(WinLossRebateQueryConditions conditions)
    {
        try
        {
            // 使用查询条件模型的验证方法
            var (isValid, errorMessage) = conditions.Validate();

            if (!isValid)
            {
                MessageBox.Show(errorMessage, @"查询条件错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 验证时间跨度（防止查询时间过长）
            var timeSpan = conditions.EndTime - conditions.StartTime;
            if (timeSpan.TotalDays > 31)
            {
                var result = MessageBox.Show(
                    $@"查询时间跨度为{timeSpan.TotalDays:F1}天，可能会影响查询性能。是否继续？",
                    @"查询确认",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                {
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"验证输赢流水回水记录查询条件时发生错误");
            MessageBox.Show(@"验证查询条件时发生错误", @"系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }

    /// <summary>
    /// 输赢流水回水记录查询文本框键盘事件处理
    /// 支持Enter键快捷查询
    /// </summary>
    private void WinLossRebateQueryTextBox_KeyDown(object? sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true; // 阻止默认的Enter键行为

            // 触发查询
            Task.Run(() =>
            {
                // 模拟点击查询按钮
                Invoke(() => { button_根据条件查询输赢流水回水记录.PerformClick(); });
            });
        }
    }

    #endregion

    #region 输赢流水回水记录查询功能

    /// <summary>
    /// 查询输赢流水回水记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">账号（可选）</param>
    /// <param name="issue">期号（可选）</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    private async Task QueryWinLossRebateRecordsAsync(DateTime startTime, DateTime endTime, string? account = null, string? issue = null, bool includeFakeUsers = false)
    {
        try
        {
            _logger.LogInformation(@"开始查询输赢流水回水记录，时间范围: {StartTime} - {EndTime}", startTime, endTime);

            // 查询输赢流水回水记录
            _winLossRebateRecordViewModels = await _winLossRebateRecordService.QueryWinLossRebateRecordsAsync(startTime, endTime, account, issue, includeFakeUsers);

            // 更新UI（确保在UI线程中执行）
            if (InvokeRequired)
            {
                Invoke(() =>
                {
                    _winLossRebateRecordBindingSource.DataSource = _winLossRebateRecordViewModels;
                    _winLossRebateRecordBindingSource.ResetBindings(false);

                    // 清除选择
                    dataGridView_输赢流水回水记录.ClearSelection();

                    _logger.LogInformation(@"输赢流水回水记录查询完成，共 {Count} 条记录", _winLossRebateRecordViewModels.Count);
                });
            }
            else
            {
                _winLossRebateRecordBindingSource.DataSource = _winLossRebateRecordViewModels;
                _winLossRebateRecordBindingSource.ResetBindings(false);

                // 清除选择
                dataGridView_输赢流水回水记录.ClearSelection();

                _logger.LogInformation(@"输赢流水回水记录查询完成，共 {Count} 条记录", _winLossRebateRecordViewModels.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询输赢流水回水记录失败");
            MessageBox.Show($@"查询输赢流水回水记录失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }



    #endregion
}
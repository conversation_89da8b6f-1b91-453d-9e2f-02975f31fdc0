namespace CommandGuard.Forms;

partial class FormLicenseCheck
{
    /// <summary>
    /// Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    /// Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    /// Required method for Designer support - do not modify
    /// the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        pictureBox1 = new PictureBox();
        label1 = new Label();
        label2 = new Label();
        textBox_MachineCode = new TextBox();
        button_CopyMachineCode = new Button();
        label3 = new Label();
        label_Status = new Label();
        label4 = new Label();
        label_LicenseInfo = new Label();


        ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
        SuspendLayout();
        //
        // pictureBox1
        //
        pictureBox1.BackColor = Color.LightBlue;
        pictureBox1.Location = new Point(30, 30);
        pictureBox1.Name = "pictureBox1";
        pictureBox1.Size = new Size(64, 64);
        pictureBox1.SizeMode = PictureBoxSizeMode.StretchImage;
        pictureBox1.TabIndex = 0;
        pictureBox1.TabStop = false;
        // 
        // label1
        // 
        label1.AutoSize = true;
        label1.Font = new Font("Microsoft YaHei UI", 16F, FontStyle.Bold);
        label1.Location = new Point(120, 40);
        label1.Name = "label1";
        label1.Size = new Size(236, 36);
        label1.TabIndex = 1;
        label1.Text = "指令卫士";
        // 
        // label2
        // 
        label2.AutoSize = true;
        label2.Font = new Font("Microsoft YaHei UI", 10F);
        label2.Location = new Point(120, 76);
        label2.Name = "label2";
        label2.Size = new Size(93, 23);
        label2.TabIndex = 2;
        label2.Text = "授权验证";
        // 
        // textBox_MachineCode
        // 
        textBox_MachineCode.Location = new Point(30, 160);
        textBox_MachineCode.Name = "textBox_MachineCode";
        textBox_MachineCode.ReadOnly = true;
        textBox_MachineCode.Size = new Size(400, 27);
        textBox_MachineCode.TabIndex = 3;
        // 
        // button_CopyMachineCode
        // 
        button_CopyMachineCode.Location = new Point(450, 158);
        button_CopyMachineCode.Name = "button_CopyMachineCode";
        button_CopyMachineCode.Size = new Size(80, 30);
        button_CopyMachineCode.TabIndex = 4;
        button_CopyMachineCode.Text = "复制";
        button_CopyMachineCode.UseVisualStyleBackColor = true;
        button_CopyMachineCode.Click += button_CopyMachineCode_Click;
        // 
        // label3
        // 
        label3.AutoSize = true;
        label3.Location = new Point(30, 137);
        label3.Name = "label3";
        label3.Size = new Size(129, 20);
        label3.TabIndex = 5;
        label3.Text = "当前机器码：";
        // 
        // label_Status
        // 
        label_Status.AutoSize = true;
        label_Status.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Bold);
        label_Status.ForeColor = Color.Red;
        label_Status.Location = new Point(30, 220);
        label_Status.Name = "label_Status";
        label_Status.Size = new Size(93, 23);
        label_Status.TabIndex = 6;
        label_Status.Text = "未找到授权";
        // 
        // label4
        // 
        label4.AutoSize = true;
        label4.Location = new Point(30, 260);
        label4.Name = "label4";
        label4.Size = new Size(129, 20);
        label4.TabIndex = 7;
        label4.Text = "授权信息：";
        // 
        // label_LicenseInfo
        // 
        label_LicenseInfo.Location = new Point(30, 290);
        label_LicenseInfo.Name = "label_LicenseInfo";
        label_LicenseInfo.Size = new Size(500, 100);
        label_LicenseInfo.TabIndex = 8;
        label_LicenseInfo.Text = "请联系软件供应商获取授权文件";


        //
        // FormLicenseCheck
        //
        AutoScaleDimensions = new SizeF(9F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(584, 420);
        Controls.Add(label_LicenseInfo);
        Controls.Add(label4);
        Controls.Add(label_Status);
        Controls.Add(label3);
        Controls.Add(button_CopyMachineCode);
        Controls.Add(textBox_MachineCode);
        Controls.Add(label2);
        Controls.Add(label1);
        Controls.Add(pictureBox1);
        FormBorderStyle = FormBorderStyle.FixedDialog;
        MaximizeBox = false;
        MinimizeBox = false;
        Name = "FormLicenseCheck";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "指令卫士 - 授权验证";
        TopMost = true;
        ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
        ResumeLayout(false);
        PerformLayout();
    }

    #endregion

    private PictureBox pictureBox1;
    private Label label1;
    private Label label2;
    private TextBox textBox_MachineCode;
    private Button button_CopyMachineCode;
    private Label label3;
    private Label label_Status;
    private Label label4;
    private Label label_LicenseInfo;

}

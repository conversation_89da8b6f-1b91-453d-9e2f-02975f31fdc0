namespace CommandGuard.ViewModels;

/// <summary>
/// 输赢流水回水记录查询条件
/// 封装输赢流水回水记录查询界面的所有查询参数
/// </summary>
public class WinLossRebateQueryConditions
{
    /// <summary>
    /// 开始时间
    /// 查询的起始时间点
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// 查询的结束时间点
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 账号
    /// 可选的账号过滤条件，为null表示不按账号过滤
    /// </summary>
    public string? Account { get; set; }

    /// <summary>
    /// 期号
    /// 可选的期号过滤条件，为null表示不按期号过滤
    /// </summary>
    public string? Issue { get; set; }

    /// <summary>
    /// 是否包含假人
    /// true表示查询结果包含假人数据，false表示只查询真人数据
    /// </summary>
    public bool IncludeFakeUsers { get; set; }

    /// <summary>
    /// 查询时间跨度
    /// 计算开始时间和结束时间之间的时间差
    /// </summary>
    public TimeSpan TimeSpan => EndTime - StartTime;

    /// <summary>
    /// 是否有账号过滤条件
    /// </summary>
    public bool HasAccountFilter => !string.IsNullOrEmpty(Account);

    /// <summary>
    /// 是否有期号过滤条件
    /// </summary>
    public bool HasIssueFilter => !string.IsNullOrEmpty(Issue);

    /// <summary>
    /// 获取查询条件的描述文本
    /// 用于日志记录和用户界面显示
    /// </summary>
    public string GetDescription()
    {
        var parts = new List<string>
        {
            $@"时间: {StartTime:yyyy-MM-dd HH:mm} - {EndTime:yyyy-MM-dd HH:mm}"
        };

        if (HasAccountFilter)
        {
            parts.Add($@"账号: {Account}");
        }

        if (HasIssueFilter)
        {
            parts.Add($@"期号: {Issue}");
        }

        parts.Add($@"包含假人: {(IncludeFakeUsers ? "是" : "否")}");

        return string.Join(@", ", parts);
    }

    /// <summary>
    /// 验证查询条件的有效性
    /// </summary>
    /// <returns>验证结果和错误信息</returns>
    public (bool IsValid, string ErrorMessage) Validate()
    {
        // 验证时间范围
        if (StartTime >= EndTime)
        {
            return (false, @"开始时间必须小于结束时间");
        }

        // 验证时间跨度
        if (TimeSpan.TotalDays > 365)
        {
            return (false, @"查询时间跨度不能超过365天");
        }

        // 验证期号格式（如果提供）
        if (HasIssueFilter)
        {
            if (Issue!.Length < 6 || !Issue.All(char.IsDigit))
            {
                return (false, @"期号格式不正确，应为6位以上数字");
            }
        }

        // 验证账号格式（如果提供）
        if (HasAccountFilter)
        {
            if (Account!.Length < 3 || Account.Length > 50)
            {
                return (false, @"账号长度应在3-50个字符之间");
            }
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 创建默认的查询条件
    /// 默认查询今天的数据，包含假人
    /// </summary>
    public static WinLossRebateQueryConditions CreateDefault()
    {
        var today = DateTime.Today;
        return new WinLossRebateQueryConditions
        {
            StartTime = today,
            EndTime = today.AddDays(1).AddSeconds(-1),
            Account = null,
            Issue = null,
            IncludeFakeUsers = true
        };
    }

    /// <summary>
    /// 创建指定日期的查询条件
    /// </summary>
    /// <param name="date">查询日期</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    public static WinLossRebateQueryConditions CreateForDate(DateTime date, bool includeFakeUsers = true)
    {
        var startOfDay = date.Date;
        return new WinLossRebateQueryConditions
        {
            StartTime = startOfDay,
            EndTime = startOfDay.AddDays(1).AddSeconds(-1),
            Account = null,
            Issue = null,
            IncludeFakeUsers = includeFakeUsers
        };
    }

    /// <summary>
    /// 创建指定时间范围的查询条件
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    public static WinLossRebateQueryConditions CreateForTimeRange(DateTime startTime, DateTime endTime, bool includeFakeUsers = true)
    {
        return new WinLossRebateQueryConditions
        {
            StartTime = startTime,
            EndTime = endTime,
            Account = null,
            Issue = null,
            IncludeFakeUsers = includeFakeUsers
        };
    }

    /// <summary>
    /// 创建指定用户的查询条件
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    public static WinLossRebateQueryConditions CreateForUser(string account, DateTime startTime, DateTime endTime, bool includeFakeUsers = true)
    {
        return new WinLossRebateQueryConditions
        {
            StartTime = startTime,
            EndTime = endTime,
            Account = account,
            Issue = null,
            IncludeFakeUsers = includeFakeUsers
        };
    }

    /// <summary>
    /// 创建指定期号的查询条件
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    public static WinLossRebateQueryConditions CreateForIssue(string issue, DateTime startTime, DateTime endTime, bool includeFakeUsers = true)
    {
        return new WinLossRebateQueryConditions
        {
            StartTime = startTime,
            EndTime = endTime,
            Account = null,
            Issue = issue,
            IncludeFakeUsers = includeFakeUsers
        };
    }
}

using System.ComponentModel;
using CommandGuard.Enums;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using CommandGuard.Services.Business;
using CommandGuard.Services.Lottery;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 手动开奖或退单窗体
/// 用于处理卡奖情况下的手动开奖和退单操作
/// </summary>
public partial class FormManualDraw : Form
{
    #region 字段和属性

    private readonly ILogger<FormManualDraw> _logger;
    private readonly IBetRecordService _betRecordService;
    private readonly IDrawService _drawService;
    private readonly IFreeSql _fSql;
    private readonly IFinancialService _financialService;
    private readonly SettlementService _settlementService;
    private readonly CancelBetService _cancelBetService;

    /// <summary>
    /// 未结算期数数据
    /// </summary>
    private List<UnsettledIssueInfo> _unsettledIssues = [];

    /// <summary>
    /// 数据绑定源
    /// </summary>
    private BindingSource _bindingSource = new();

    #endregion

    #region 构造函数

    public FormManualDraw(
        ILogger<FormManualDraw> logger,
        IBetRecordService betRecordService,
        IDrawService drawService,
        IFreeSql fSql,
        IFinancialService financialService,
        SettlementService settlementService,
        CancelBetService cancelBetService)
    {
        _logger = logger;
        _betRecordService = betRecordService;
        _drawService = drawService;
        _fSql = fSql;
        _financialService = financialService;
        _settlementService = settlementService;
        _cancelBetService = cancelBetService;

        InitializeComponent();
        InitializeDataGrid();
    }

    #endregion

    #region 初始化方法

    /// <summary>
    /// 初始化数据表格
    /// </summary>
    private void InitializeDataGrid()
    {
        // 设置数据源
        dataGridView_UnsettledIssues.DataSource = _bindingSource;

        // 应用与投注记录表格相同的基础样式配置
        ApplyBetRecordTableStyle();

        // 添加列
        AddDataGridViewColumns();

        // 绑定格式化事件
        dataGridView_UnsettledIssues.CellFormatting += DataGridView_UnsettledIssues_CellFormatting;
    }

    /// <summary>
    /// 应用与投注记录表格相同的基础样式配置
    /// </summary>
    private void ApplyBetRecordTableStyle()
    {
        // 基础设置
        dataGridView_UnsettledIssues.AutoGenerateColumns = false;
        dataGridView_UnsettledIssues.AllowUserToAddRows = false;
        dataGridView_UnsettledIssues.AllowUserToDeleteRows = false;
        dataGridView_UnsettledIssues.ReadOnly = true;
        dataGridView_UnsettledIssues.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        dataGridView_UnsettledIssues.MultiSelect = false;
        dataGridView_UnsettledIssues.AllowUserToResizeColumns = false;
        dataGridView_UnsettledIssues.AllowUserToResizeRows = false;
        dataGridView_UnsettledIssues.RowHeadersVisible = false;

        // 滚动条设置
        dataGridView_UnsettledIssues.ScrollBars = ScrollBars.Vertical;
        dataGridView_UnsettledIssues.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;

        // 表头样式（与投注记录表格一致）
        dataGridView_UnsettledIssues.EnableHeadersVisualStyles = false;
        dataGridView_UnsettledIssues.ColumnHeadersDefaultCellStyle.BackColor = Color.LightGray;
        dataGridView_UnsettledIssues.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
        dataGridView_UnsettledIssues.ColumnHeadersDefaultCellStyle.Font = new Font(@"Microsoft YaHei", 9F, FontStyle.Bold);
        dataGridView_UnsettledIssues.ColumnHeadersHeight = 24;
        dataGridView_UnsettledIssues.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;

        // 行样式
        dataGridView_UnsettledIssues.RowTemplate.Height = 22;
        dataGridView_UnsettledIssues.DefaultCellStyle.Font = new Font(@"Microsoft YaHei", 9F);
        dataGridView_UnsettledIssues.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
        dataGridView_UnsettledIssues.DefaultCellStyle.SelectionForeColor = Color.Black;

        // 背景色设置
        dataGridView_UnsettledIssues.BackgroundColor = Color.White;
        dataGridView_UnsettledIssues.BorderStyle = BorderStyle.Fixed3D;

        // 清空现有列
        dataGridView_UnsettledIssues.Columns.Clear();
    }

    /// <summary>
    /// 添加数据表格列
    /// </summary>
    private void AddDataGridViewColumns()
    {
        // 期号列
        var issueColumn = new DataGridViewTextBoxColumn
        {
            Name = "Issue",
            HeaderText = @"期号",
            DataPropertyName = nameof(UnsettledIssueInfo.Issue),
            Width = 150,
            DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
        };
        dataGridView_UnsettledIssues.Columns.Add(issueColumn);

        // 未结算金额列
        var amountColumn = new DataGridViewTextBoxColumn
        {
            Name = "UnsettledAmount",
            HeaderText = @"未结算金额",
            DataPropertyName = nameof(UnsettledIssueInfo.UnsettledAmount),
            Width = 120,
            DefaultCellStyle = { 
                Alignment = DataGridViewContentAlignment.MiddleRight,
                Format = "F2"
            }
        };
        dataGridView_UnsettledIssues.Columns.Add(amountColumn);

        // 注数列
        var countColumn = new DataGridViewTextBoxColumn
        {
            Name = "BetCount",
            HeaderText = @"注数",
            DataPropertyName = nameof(UnsettledIssueInfo.BetCount),
            Width = 80,
            DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
        };
        dataGridView_UnsettledIssues.Columns.Add(countColumn);
    }

    /// <summary>
    /// 单元格格式化事件处理
    /// 实现与投注记录表格相同的交替行颜色样式
    /// </summary>
    private void DataGridView_UnsettledIssues_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (sender is not DataGridView grid || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            // 设置行的背景色（交替行颜色，与投注记录表格一致）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }

                // 设置默认前景色
                e.CellStyle.ForeColor = Color.Black;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"未结算期数表格格式化失败，行: {Row}, 列: {Column}", e.RowIndex, e.ColumnIndex);
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 窗体加载事件
    /// </summary>
    private async void FormManualDraw_Load(object sender, EventArgs e)
    {
        try
        {
            await LoadUnsettledIssuesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载未结算期数数据时发生异常");
            MessageBox.Show($@"加载数据失败：{ex.Message}", @"错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 手动录入开奖并结算按钮点击事件
    /// </summary>
    private async void button_ManualDraw_Click(object sender, EventArgs e)
    {
        try
        {
            var selectedIssue = GetSelectedIssue();
            if (selectedIssue == null)
            {
                MessageBox.Show(@"请选择要开奖的期号", @"提示", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            await HandleManualDrawAsync(selectedIssue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"手动开奖时发生异常");
            MessageBox.Show($@"手动开奖失败：{ex.Message}", @"错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 撤销并退款按钮点击事件
    /// </summary>
    private async void button_CancelAndRefund_Click(object sender, EventArgs e)
    {
        try
        {
            var selectedIssue = GetSelectedIssue();
            if (selectedIssue == null)
            {
                MessageBox.Show(@"请选择要撤销的期号", @"提示", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            await HandleCancelAndRefundAsync(selectedIssue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"撤销退款时发生异常");
            MessageBox.Show($@"撤销退款失败：{ex.Message}", @"错误", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region 业务方法

    /// <summary>
    /// 加载未结算期数数据
    /// </summary>
    private async Task LoadUnsettledIssuesAsync()
    {
        _logger.LogInformation(@"开始加载未结算期数数据");

        // 获取未结算的投注订单
        var unsettledBets = await _fSql.Select<BetOrder>()
            .Where(b => b.Status == Enums.EnumBetOrderStatus.Confirmed) // 已确认但未结算
            .ToListAsync();

        // 按期号分组统计
        var unsettledIssues = unsettledBets
            .GroupBy(b => b.Issue)
            .Select(g => new UnsettledIssueInfo
            {
                Issue = g.Key,
                UnsettledAmount = g.Sum(b => b.Amount),
                BetCount = g.Count()
            })
            .ToList();

        _unsettledIssues = unsettledIssues.OrderBy(u => u.Issue).ToList();
        _bindingSource.DataSource = _unsettledIssues;

        _logger.LogInformation(@"加载完成，共 {Count} 个未结算期数", _unsettledIssues.Count);
    }

    /// <summary>
    /// 获取当前选中的期数
    /// </summary>
    private UnsettledIssueInfo? GetSelectedIssue()
    {
        if (dataGridView_UnsettledIssues.SelectedRows.Count == 0)
            return null;

        var selectedIndex = dataGridView_UnsettledIssues.SelectedRows[0].Index;
        return selectedIndex >= 0 && selectedIndex < _unsettledIssues.Count 
            ? _unsettledIssues[selectedIndex] 
            : null;
    }

    /// <summary>
    /// 处理手动开奖
    /// </summary>
    private async Task HandleManualDrawAsync(UnsettledIssueInfo selectedIssue)
    {
        try
        {
            _logger.LogInformation(@"开始处理手动开奖，期号：{Issue}", selectedIssue.Issue);

            // 1. 弹出开奖号码输入对话框
            using var inputForm = new FormDrawNumberInput(selectedIssue.Issue);
            if (inputForm.ShowDialog(this) != DialogResult.OK)
            {
                _logger.LogInformation(@"用户取消了开奖号码输入");
                return;
            }

            var drawNumbers = inputForm.DrawNumbers;
            _logger.LogInformation(@"用户输入的开奖号码：{DrawNumbers}", drawNumbers);

            // 2. 确认操作
            var confirmResult = MessageBox.Show(
                $@"确定要为期号 {selectedIssue.Issue} 录入开奖号码并结算吗？

开奖号码：{drawNumbers}
未结算金额：{selectedIssue.UnsettledAmount:F2}
注数：{selectedIssue.BetCount}

此操作不可撤销！",
                @"确认手动开奖",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (confirmResult != DialogResult.Yes)
            {
                _logger.LogInformation(@"用户取消了手动开奖操作");
                return;
            }

            // 3. 执行手动开奖
            await ExecuteManualDrawAsync(selectedIssue.Issue, drawNumbers);

            // 4. 刷新数据
            await LoadUnsettledIssuesAsync();

            MessageBox.Show($@"期号 {selectedIssue.Issue} 手动开奖完成！", @"成功",
                MessageBoxButtons.OK, MessageBoxIcon.Information);

            _logger.LogInformation(@"手动开奖完成，期号：{Issue}", selectedIssue.Issue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"手动开奖处理失败，期号：{Issue}", selectedIssue.Issue);
            throw;
        }
    }

    /// <summary>
    /// 处理撤销并退款
    /// </summary>
    private async Task HandleCancelAndRefundAsync(UnsettledIssueInfo selectedIssue)
    {
        try
        {
            _logger.LogInformation(@"开始处理撤销退款，期号：{Issue}", selectedIssue.Issue);

            // 1. 确认操作
            var confirmResult = MessageBox.Show(
                $@"确定要撤销期号 {selectedIssue.Issue} 的所有投注并退款吗？

未结算金额：{selectedIssue.UnsettledAmount:F2}
注数：{selectedIssue.BetCount}

此操作将：
• 撤销该期所有投注订单
• 退还用户投注金额
• 此操作不可撤销！",
                @"确认撤销退款",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (confirmResult != DialogResult.Yes)
            {
                _logger.LogInformation(@"用户取消了撤销退款操作");
                return;
            }

            // 2. 执行撤销退款（使用公共服务，包含群聊通知）
            var result = await _cancelBetService.CancelBetsByIssueAsync(selectedIssue.Issue, @"管理员", true);

            // 3. 刷新数据
            await LoadUnsettledIssuesAsync();

            // 4. 显示结果
            if (result.Success)
            {
                MessageBox.Show($@"期号 {selectedIssue.Issue} 撤销退款完成！

撤销订单数：{result.CancelledOrderCount}
退款总额：{result.TotalRefundAmount:F2}
涉及用户：{result.AffectedUsers.Count}个", @"撤销成功",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show($@"撤销退款失败：{result.Message}", @"错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            _logger.LogInformation(@"撤销退款完成，期号：{Issue}", selectedIssue.Issue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"撤销退款处理失败，期号：{Issue}", selectedIssue.Issue);
            throw;
        }
    }

    /// <summary>
    /// 执行手动开奖
    /// </summary>
    private async Task ExecuteManualDrawAsync(string issue, string drawNumbers)
    {
        try
        {
            // 1. 保存开奖数据
            var kaiJiang = new KaiJiang
            {
                Issue = issue,
                DrawNum = drawNumbers,
                Time = DateTime.Now.ToString(@"yyyy-MM-dd HH:mm:ss")
            };

            await _fSql.Insert(kaiJiang).ExecuteAffrowsAsync();
            _logger.LogInformation(@"保存开奖数据成功，期号：{Issue}，开奖号码：{DrawNumbers}", issue, drawNumbers);

            // 2. 获取该期所有未结算的投注
            var unsettledBets = await _fSql.Select<BetOrder>()
                .Where(b => b.Issue == issue && b.Status == EnumBetOrderStatus.Confirmed)
                .ToListAsync();

            _logger.LogInformation(@"获取到 {Count} 个未结算投注，期号：{Issue}", unsettledBets.Count, issue);

            // 3. 逐个结算投注
            foreach (var bet in unsettledBets)
            {
                // 计算投注结果
                var fanTanResult = _settlementService.CalculateFanTanResult(drawNumbers);
                var betResult = _settlementService.JudgeBetResult(bet.PlayItem, fanTanResult);

                // 更新投注订单
                bet.DrawResult = drawNumbers;
                bet.ActualWinAmount = betResult == EnumBetResult.Win ? bet.PotentialWinAmount : 0;
                bet.Status = betResult == EnumBetResult.Win ? EnumBetOrderStatus.Win : EnumBetOrderStatus.Lose;
                bet.SettledTime = DateTime.Now;

                await _fSql.Update<BetOrder>()
                    .SetSource(bet)
                    .ExecuteAffrowsAsync();

                // 如果中奖，派发奖金
                if (betResult == EnumBetResult.Win && bet.ActualWinAmount.HasValue && bet.ActualWinAmount.Value > 0)
                {
                    await _financialService.IncreaseBalanceAsync(
                        bet.Account,
                        bet.ActualWinAmount.Value,
                        @"BetWin",
                        bet.Id,
                        @"投注中奖",
                        @"系统");
                }

                _logger.LogDebug(@"结算投注完成，订单ID：{OrderId}，中奖金额：{WinAmount}",
                    bet.Id, bet.ActualWinAmount);
            }

            _logger.LogInformation(@"手动开奖完成，期号：{Issue}", issue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"手动开奖失败，期号：{Issue}", issue);
            throw;
        }
    }



    #endregion
}

/// <summary>
/// 未结算期数信息
/// </summary>
public class UnsettledIssueInfo
{
    /// <summary>
    /// 期号
    /// </summary>
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 未结算金额
    /// </summary>
    public decimal UnsettledAmount { get; set; }

    /// <summary>
    /// 注数
    /// </summary>
    public int BetCount { get; set; }
}

namespace CommandGuard.Forms
{
    partial class FormDrawHistory
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            pictureBox_DrawHistory = new System.Windows.Forms.PictureBox();
            ((System.ComponentModel.ISupportInitialize)pictureBox_DrawHistory).BeginInit();
            SuspendLayout();
            // 
            // pictureBox_DrawHistory
            // 
            pictureBox_DrawHistory.BackColor = System.Drawing.Color.White;
            pictureBox_DrawHistory.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            pictureBox_DrawHistory.Dock = System.Windows.Forms.DockStyle.Fill;
            pictureBox_DrawHistory.Location = new System.Drawing.Point(0, 0);
            pictureBox_DrawHistory.Name = "pictureBox_DrawHistory";
            pictureBox_DrawHistory.Size = new System.Drawing.Size(600, 400);
            pictureBox_DrawHistory.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            pictureBox_DrawHistory.TabIndex = 1;
            pictureBox_DrawHistory.TabStop = false;
            // 
            // FormDrawHistory
            // 
            AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            AutoSize = true;
            AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            ClientSize = new System.Drawing.Size(600, 400);
            Controls.Add(pictureBox_DrawHistory);
            FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            KeyPreview = true;
            MaximizeBox = false;
            MinimumSize = new System.Drawing.Size(300, 200);
            StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            Text = "开奖历史";
            FormClosing += FormDrawHistory_FormClosing;
            Load += FormDrawHistory_Load;
            KeyDown += FormDrawHistory_KeyDown;
            ((System.ComponentModel.ISupportInitialize)pictureBox_DrawHistory).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private System.Windows.Forms.PictureBox pictureBox_DrawHistory;
    }
}

using CommandGuard.Enums;
using CommandGuard.Interfaces.Lottery;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Lottery;

/// <summary>
/// 游戏结算服务
/// 负责根据开奖结果计算游戏的输赢结果
/// 支持多彩种的不同番摊计算方式
/// </summary>
public class SettlementService(
    ILogger<SettlementService> logger,
    ILotteryConfigurationService lotteryConfig)
{
    /// <summary>
    /// 解析宾果开奖号码 - 开奖数据预处理（通用方法）
    ///
    /// 功能：
    /// - 解析开奖号码字符串为数字数组
    /// - 验证号码格式和数量的有效性
    /// - 过滤无效字符和格式错误
    /// - 支持所有宾果彩种的号码解析
    ///
    /// 输入格式：
    /// - 逗号分隔的号码字符串
    /// - 例如："01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21"
    ///
    /// 验证规则：
    /// - 必须包含21个有效号码（支持所有宾果彩种）
    /// - 每个号码必须在1-80范围内
    /// - 格式必须正确
    /// </summary>
    /// <param name="drawNumbers">开奖号码字符串</param>
    /// <returns>解析后的号码数组和是否有效的元组</returns>
    public (int[] numbers, bool isValid) ParseBingoNumbers(string drawNumbers)
    {
        try
        {
            if (string.IsNullOrEmpty(drawNumbers))
            {
                logger.LogWarning(@"开奖号码为空");
                return ([], false);
            }

            // 解析开奖号码
            var numbers = drawNumbers.Split(',')
                .Take(21) // 取前21个号码（支持所有宾果彩种）
                .Select(n => int.TryParse(n.Trim(), out var num) ? num : 0)
                .Where(n => n > 0)
                .ToArray();

            bool isValid = numbers.Length >= 20; // 至少需要20个号码
            if (!isValid)
            {
                logger.LogWarning(@"开奖号码数量不足，期望至少20个，实际{Count}个", numbers.Length);
            }

            return (numbers, isValid);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"解析开奖号码时发生异常");
            return ([], false);
        }
    }

    /// <summary>
    /// 解析台湾宾果开奖号码 - 兼容性方法（保持向后兼容）
    /// 调用新的通用解析方法
    /// </summary>
    /// <param name="drawNumbers">开奖号码字符串</param>
    /// <returns>解析后的号码数组和是否有效的元组</returns>
    public (int[] numbers, bool isValid) ParseTaiwanBingoNumbers(string drawNumbers)
    {
        return ParseBingoNumbers(drawNumbers);
    }

    /// <summary>
    /// 提取前N个号码字符串
    /// </summary>
    /// <param name="drawNumbers">开奖号码字符串</param>
    /// <param name="count">提取数量，默认20个</param>
    /// <returns>提取的号码字符串</returns>
    public string ExtractNumberString(string drawNumbers, int count = 20)
    {
        try
        {
            if (string.IsNullOrEmpty(drawNumbers))
            {
                return string.Empty;
            }

            var numbers = drawNumbers.Split(',')
                .Take(count)
                .Where(n => !string.IsNullOrWhiteSpace(n))
                .ToArray();

            return string.Join(",", numbers);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"提取号码字符串时发生异常");
            return string.Empty;
        }
    }

    /// <summary>
    /// 计算番摊开奖结果 - 核心结算算法（使用当前彩种）
    /// 根据当前选择的彩种自动选择对应的计算方式
    /// </summary>
    /// <param name="bingoNumbers">宾果开奖号码字符串</param>
    /// <returns>番摊开奖结果（1-4）</returns>
    public async Task<int> CalculateFanTanResultAsync(string bingoNumbers)
    {
        try
        {
            var currentLottery = await lotteryConfig.GetCurrentLotteryAsync();
            return await CalculateFanTanResultAsync(bingoNumbers, currentLottery);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"计算番摊开奖结果时发生异常");
            return 0;
        }
    }

    /// <summary>
    /// 计算番摊开奖结果 - 指定彩种的核心结算算法
    ///
    /// 功能：
    /// - 根据不同彩种使用不同的计算规则
    /// - 确保结果的随机性和公平性
    /// - 支持三种宾果彩种的计算方式
    ///
    /// 计算规则：
    /// - 宾果1：直接采用第21个号码除以4
    /// - 宾果2：取第1个+第20个+第21个之和除以4
    /// - 宾果3：取前20个号码之和除以4（原台湾宾果）
    ///
    /// 业务意义：
    /// - 1: 对应番摊的"一"
    /// - 2: 对应番摊的"二"
    /// - 3: 对应番摊的"三"
    /// - 4: 对应番摊的"四"
    /// </summary>
    /// <param name="bingoNumbers">宾果开奖号码字符串</param>
    /// <param name="lottery">彩种类型</param>
    /// <returns>番摊开奖结果（1-4）</returns>
    public async Task<int> CalculateFanTanResultAsync(string bingoNumbers, EnumLottery lottery)
    {
        try
        {
            // 使用统一的号码解析方法
            var (numbers, isValid) = ParseBingoNumbers(bingoNumbers);

            if (!isValid || numbers.Length == 0)
            {
                logger.LogWarning(@"开奖号码解析失败或为空: {Numbers}", bingoNumbers);
                return 0;
            }

            int sum;
            string calculationMethod;

            // 根据彩种选择不同的计算方式
            switch (lottery)
            {
                case EnumLottery.宾果1:
                    // 宾果1：直接采用第21个号码除以4
                    if (numbers.Length < 21)
                    {
                        logger.LogWarning(@"宾果1计算需要21个号码，实际只有{Count}个", numbers.Length);
                        return 0;
                    }
                    sum = numbers[20]; // 第21个号码（索引20）
                    calculationMethod = @"第21个号码";
                    break;

                case EnumLottery.宾果2:
                    // 宾果2：取第1个+第20个+第21个之和除以4
                    if (numbers.Length < 21)
                    {
                        logger.LogWarning(@"宾果2计算需要21个号码，实际只有{Count}个", numbers.Length);
                        return 0;
                    }
                    sum = numbers[0] + numbers[19] + numbers[20]; // 第1、20、21个号码
                    calculationMethod = @"第1+第20+第21个号码之和";
                    break;

                case EnumLottery.宾果3:
                    // 宾果3：取前20个号码之和除以4（原台湾宾果）
                    if (numbers.Length < 20)
                    {
                        logger.LogWarning(@"宾果3计算需要至少20个号码，实际只有{Count}个", numbers.Length);
                        return 0;
                    }
                    sum = numbers.Take(20).Sum(); // 前20个号码之和
                    calculationMethod = @"前20个号码之和";
                    break;

                default:
                    logger.LogError(@"不支持的彩种类型: {Lottery}", lottery);
                    return 0;
            }

            // 计算除四的余数
            var remainder = sum % 4;

            // 转换为番摊结果（余数0对应番摊4）
            var fanTanResult = remainder == 0 ? 4 : remainder;

            logger.LogDebug(@"{Lottery} 番摊计算: {Method} = {Sum}, 除四余数: {Remainder}, 番摊结果: {Result}",
                lottery, calculationMethod, sum, remainder, fanTanResult);

            return fanTanResult;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"计算{Lottery}番摊开奖结果时发生异常", lottery);
            return 0;
        }
    }

    /// <summary>
    /// 计算番摊开奖结果 - 兼容性方法（保持向后兼容）
    /// 默认使用宾果3的计算方式（原台湾宾果）
    /// </summary>
    /// <param name="taiwanBingoNumbers">台湾宾果开奖号码（前20个号码）</param>
    /// <returns>番摊开奖结果（1-4）</returns>
    public int CalculateFanTanResult(string taiwanBingoNumbers)
    {
        try
        {
            // 为了保持向后兼容，直接使用宾果3的计算方式
            var task = CalculateFanTanResultAsync(taiwanBingoNumbers, EnumLottery.宾果3);
            return task.GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"计算番摊开奖结果时发生异常（兼容性方法）");
            return 0;
        }
    }

    /// <summary>
    /// 判断投注项目的输赢结果 - 投注结算核心逻辑
    ///
    /// 功能：
    /// - 根据投注项目和开奖结果判断输赢
    /// - 支持所有投注类型的结算
    /// - 返回标准的投注结果枚举
    ///
    /// 支持的投注类型：
    /// - 基础投注：大、小、单、双
    /// - 特殊投注：豹子、对子、顺子
    /// - 正码投注：1、2、3、4
    /// - 念码投注：1念2、1念3等
    /// - 三门投注：123、124、134、234
    ///
    /// 结算规则：
    /// - Win: 投注项目与开奖结果匹配
    /// - Loss: 投注项目与开奖结果不匹配
    /// - Push: 平局（某些特殊情况）
    /// </summary>
    /// <param name="playItem">投注项目名称</param>
    /// <param name="fanTanResult">番摊开奖结果（1-4）</param>
    /// <returns>投注结果（Win/Loss/Push）</returns>
    public EnumBetResult JudgeBetResult(string playItem, int fanTanResult)
    {
        try
        {
            if (fanTanResult < 1 || fanTanResult > 4)
            {
                logger.LogWarning(@"无效的番摊开奖结果: {Result}", fanTanResult);
                return EnumBetResult.Loss;
            }

            return playItem switch
            {
                // 单双判断
                "单" => (fanTanResult == 1 || fanTanResult == 3) ? EnumBetResult.Win : EnumBetResult.Loss,
                "双" => (fanTanResult == 2 || fanTanResult == 4) ? EnumBetResult.Win : EnumBetResult.Loss,

                // 正码判断
                "1正" => JudgeZhengResult(1, fanTanResult),
                "2正" => JudgeZhengResult(2, fanTanResult),
                "3正" => JudgeZhengResult(3, fanTanResult),
                "4正" => JudgeZhengResult(4, fanTanResult),

                // 番码判断
                "1番" => fanTanResult == 1 ? EnumBetResult.Win : EnumBetResult.Loss,
                "2番" => fanTanResult == 2 ? EnumBetResult.Win : EnumBetResult.Loss,
                "3番" => fanTanResult == 3 ? EnumBetResult.Win : EnumBetResult.Loss,
                "4番" => fanTanResult == 4 ? EnumBetResult.Win : EnumBetResult.Loss,

                // 角码判断
                "12角" => (fanTanResult == 1 || fanTanResult == 2) ? EnumBetResult.Win : EnumBetResult.Loss,
                "23角" => (fanTanResult == 2 || fanTanResult == 3) ? EnumBetResult.Win : EnumBetResult.Loss,
                "34角" => (fanTanResult == 3 || fanTanResult == 4) ? EnumBetResult.Win : EnumBetResult.Loss,
                "14角" => (fanTanResult == 1 || fanTanResult == 4) ? EnumBetResult.Win : EnumBetResult.Loss,

                // 念码判断
                var nianItem when nianItem.Contains("念") => JudgeNianResult(nianItem, fanTanResult),

                // 未知投注项目
                _ => EnumBetResult.Loss
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"判断投注结果时发生异常，投注项目: {PlayItem}, 开奖结果: {Result}", 
                playItem, fanTanResult);
            return EnumBetResult.Loss;
        }
    }

    /// <summary>
    /// 判断正码的输赢结果
    /// </summary>
    /// <param name="zhengNumber">正码号码（1-4）</param>
    /// <param name="fanTanResult">番摊开奖结果</param>
    /// <returns>投注结果</returns>
    private EnumBetResult JudgeZhengResult(int zhengNumber, int fanTanResult)
    {
        return zhengNumber switch
        {
            1 => fanTanResult switch
            {
                1 => EnumBetResult.Win,    // 中奖
                3 => EnumBetResult.Loss,   // 不中奖
                _ => EnumBetResult.Draw    // 和局
            },
            2 => fanTanResult switch
            {
                2 => EnumBetResult.Win,    // 中奖
                4 => EnumBetResult.Loss,   // 不中奖
                _ => EnumBetResult.Draw    // 和局
            },
            3 => fanTanResult switch
            {
                3 => EnumBetResult.Win,    // 中奖
                1 => EnumBetResult.Loss,   // 不中奖
                _ => EnumBetResult.Draw    // 和局
            },
            4 => fanTanResult switch
            {
                4 => EnumBetResult.Win,    // 中奖
                2 => EnumBetResult.Loss,   // 不中奖
                _ => EnumBetResult.Draw    // 和局
            },
            _ => EnumBetResult.Loss
        };
    }

    /// <summary>
    /// 判断念码的输赢结果
    /// </summary>
    /// <param name="nianItem">念码项目（如"1念2"）</param>
    /// <param name="fanTanResult">番摊开奖结果</param>
    /// <returns>投注结果</returns>
    private EnumBetResult JudgeNianResult(string nianItem, int fanTanResult)
    {
        try
        {
            // 解析念码，格式如"1念2"
            var parts = nianItem.Split('念');
            if (parts.Length != 2 || 
                !int.TryParse(parts[0], out var aNumber) || 
                !int.TryParse(parts[1], out var bNumber))
            {
                logger.LogWarning(@"无效的念码格式: {NianItem}", nianItem);
                return EnumBetResult.Loss;
            }

            // A念B：开奖结果与A相同时中奖，与B相同时和局，其他不中奖
            if (fanTanResult == aNumber)
            {
                return EnumBetResult.Win;   // 中奖
            }
            else if (fanTanResult == bNumber)
            {
                return EnumBetResult.Draw;  // 和局
            }
            else
            {
                return EnumBetResult.Loss;  // 不中奖
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"判断念码结果时发生异常: {NianItem}", nianItem);
            return EnumBetResult.Loss;
        }
    }

    /// <summary>
    /// 获取投注项目的详细说明
    /// </summary>
    /// <param name="playItem">投注项目</param>
    /// <returns>项目说明</returns>
    public string GetPlayItemDescription(string playItem)
    {
        return playItem switch
        {
            "单" => "开奖结果为单数（1或3）时中奖",
            "双" => "开奖结果为双数（2或4）时中奖",
            
            "1正" => "开奖结果为1时中奖，为3时不中奖，其他和局",
            "2正" => "开奖结果为2时中奖，为4时不中奖，其他和局",
            "3正" => "开奖结果为3时中奖，为1时不中奖，其他和局",
            "4正" => "开奖结果为4时中奖，为2时不中奖，其他和局",
            
            "1番" => "开奖结果为1时中奖",
            "2番" => "开奖结果为2时中奖",
            "3番" => "开奖结果为3时中奖",
            "4番" => "开奖结果为4时中奖",
            
            "12角" => "开奖结果为1或2时中奖",
            "23角" => "开奖结果为2或3时中奖",
            "34角" => "开奖结果为3或4时中奖",
            "14角" => "开奖结果为1或4时中奖",
            
            var nianItem when nianItem.Contains("念") => $"开奖结果与前号相同时中奖，与后号相同时和局",
            
            _ => "未知投注项目"
        };
    }
}

using CommandGuard.Enums;

namespace CommandGuard.ViewModels;

/// <summary>
/// 下分申请视图模型
/// 包含昵称信息用于界面显示
/// </summary>
public class WithdrawRequestViewModel
{
    /// <summary>
    /// 申请ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 用户账号
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 申请金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 申请状态
    /// </summary>
    public EnumWithdrawStatus Status { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 获取状态描述
    /// </summary>
    /// <returns>状态的中文描述</returns>
    public string GetStatusDescription()
    {
        return Status switch
        {
            EnumWithdrawStatus.Pending => @"待审核",
            EnumWithdrawStatus.Approved => @"已通过",
            EnumWithdrawStatus.Rejected => @"已拒绝",
            _ => @"未知状态"
        };
    }

    /// <summary>
    /// 获取用户显示名称
    /// 优先显示昵称，如果昵称为空则显示账号
    /// </summary>
    /// <returns>用户显示名称</returns>
    public string GetDisplayName()
    {
        return string.IsNullOrWhiteSpace(NickName) ? Account : NickName;
    }

    /// <summary>
    /// 获取格式化的金额显示
    /// </summary>
    /// <returns>格式化的金额字符串</returns>
    public string GetFormattedAmount()
    {
        return Amount.ToString(@"F2");
    }

    /// <summary>
    /// 获取格式化的创建时间显示
    /// </summary>
    /// <returns>格式化的时间字符串</returns>
    public string GetFormattedCreatedTime()
    {
        return CreatedTime.ToString(@"yyyy-MM-dd HH:mm:ss");
    }

    /// <summary>
    /// 判断是否可以取消申请
    /// </summary>
    /// <returns>是否可以取消</returns>
    public bool CanCancel()
    {
        return Status == EnumWithdrawStatus.Pending;
    }

    /// <summary>
    /// 判断申请是否已完成（通过或拒绝）
    /// </summary>
    /// <returns>是否已完成</returns>
    public bool IsCompleted()
    {
        return Status == EnumWithdrawStatus.Approved || Status == EnumWithdrawStatus.Rejected;
    }
}

using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 投注记录页面
/// </summary>
public partial class FormMain
{
    #region 投注记录查询界面事件处理

    /// <summary>
    /// 查询按钮点击事件处理
    /// </summary>
    private async void button_根据选择条件查询投注记录_Click(object sender, EventArgs e)
    {
        try
        {
            // 禁用查询按钮，防止重复点击
            button_根据选择条件查询投注记录.Enabled = false;
            button_根据选择条件查询投注记录.Text = @"查询中...";

            // 获取查询条件
            var queryConditions = GetBetRecordQueryConditions();

            // 验证查询条件
            if (!ValidateBetRecordQueryConditions(queryConditions))
            {
                return;
            }

            // 执行查询
            await QueryBetRecordsAsync(
                queryConditions.StartTime,
                queryConditions.EndTime,
                queryConditions.Issue,
                queryConditions.Account,
                queryConditions.IncludeFakeUsers
            );

            _logger.LogInformation(@"投注记录查询完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询投注记录时发生错误");
            MessageBox.Show($@"查询失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 恢复查询按钮状态
            button_根据选择条件查询投注记录.Enabled = true;
            button_根据选择条件查询投注记录.Text = @"查询";
        }
    }

    /// <summary>
    /// 获取投注记录查询条件
    /// </summary>
    private BetRecordQueryConditions GetBetRecordQueryConditions()
    {
        try
        {
            // 获取开始时间
            var startDate = dateTimePicker_查询投注记录开始日期.Value.Date;
            var startHour = (int)numericUpDown_查询投注记录开始小时.Value;
            var startMinute = (int)numericUpDown_查询投注记录开始分钟.Value;
            var startTime = startDate.AddHours(startHour).AddMinutes(startMinute);

            // 获取结束时间
            var endDate = dateTimePicker_查询投注记录结束日期.Value.Date;
            var endHour = (int)numericUpDown_查询投注记录结束小时.Value;
            var endMinute = (int)numericUpDown_查询投注记录结束分钟.Value;
            var endTime = endDate.AddHours(endHour).AddMinutes(endMinute);

            // 获取其他条件
            var issue = textBox_根据期号查询投注记录.Text.Trim();
            var account = textBox_根据账号查询投注记录.Text.Trim();
            var includeFakeUsers = checkBox_查询投注记录包含假人.Checked;

            return new BetRecordQueryConditions
            {
                StartTime = startTime,
                EndTime = endTime,
                Issue = string.IsNullOrEmpty(issue) ? null : issue,
                Account = string.IsNullOrEmpty(account) ? null : account,
                IncludeFakeUsers = includeFakeUsers
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"获取查询条件时发生错误");
            throw new InvalidOperationException(@"获取查询条件失败", ex);
        }
    }

    /// <summary>
    /// 验证投注记录查询条件
    /// </summary>
    private bool ValidateBetRecordQueryConditions(BetRecordQueryConditions conditions)
    {
        try
        {
            // 验证时间范围
            if (conditions.StartTime >= conditions.EndTime)
            {
                MessageBox.Show(@"开始时间必须小于结束时间", @"查询条件错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // 验证时间跨度（防止查询时间过长）
            var timeSpan = conditions.EndTime - conditions.StartTime;
            if (timeSpan.TotalDays > 31)
            {
                var result = MessageBox.Show(
                    $@"查询时间跨度为{timeSpan.TotalDays:F1}天，可能会影响查询性能。是否继续？",
                    @"查询确认",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                {
                    return false;
                }
            }

            // 验证期号格式（如果提供）
            if (!string.IsNullOrEmpty(conditions.Issue))
            {
                if (conditions.Issue.Length < 6 || !conditions.Issue.All(char.IsDigit))
                {
                    MessageBox.Show(@"期号格式不正确，应为6位以上数字", @"查询条件错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            // 验证账号格式（如果提供）
            if (!string.IsNullOrEmpty(conditions.Account))
            {
                if (conditions.Account.Length < 3)
                {
                    MessageBox.Show(@"账号长度不能少于3个字符", @"查询条件错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"验证查询条件时发生错误");
            MessageBox.Show(@"验证查询条件时发生错误", @"系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }

    /// <summary>
    /// 投注记录查询文本框键盘事件处理
    /// 支持Enter键快捷查询
    /// </summary>
    private void BetRecordQueryTextBox_KeyDown(object? sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true; // 阻止默认的Enter键行为

            // 触发查询
            Task.Run(() =>
            {
                // 模拟点击查询按钮
                Invoke(() => { button_根据选择条件查询投注记录.PerformClick(); });
            });
        }
    }

    #endregion

    #region 投注记录查询功能

    /// <summary>
    /// 查询投注记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="issue">期号（可选）</param>
    /// <param name="account">账号（可选）</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    public async Task QueryBetRecordsAsync(DateTime startTime, DateTime endTime, string? issue = null, string? account = null, bool includeFakeUsers = false)
    {
        try
        {
            _logger.LogInformation(@"开始查询投注记录，时间范围: {StartTime} - {EndTime}", startTime, endTime);

            // 确保投注记录表格已初始化
            // EnsureBetRecordGridInitialized();

            // 查询投注记录
            _betRecordViewModels = await _betRecordService.QueryBetRecordsAsync(startTime, endTime, issue, account, includeFakeUsers);

            // 更新UI（确保在UI线程中执行）
            if (InvokeRequired)
            {
                Invoke(() =>
                {
                    _betRecordBindingSource.DataSource = _betRecordViewModels;
                    _betRecordBindingSource.ResetBindings(false);

                    // 清除选择
                    dataGridView_投注记录.ClearSelection();

                    // 更新统计信息
                    UpdateBetRecordStatistics();

                    _logger.LogInformation(@"投注记录查询完成，共 {Count} 条记录", _betRecordViewModels.Count);
                });
            }
            else
            {
                _betRecordBindingSource.DataSource = _betRecordViewModels;
                _betRecordBindingSource.ResetBindings(false);

                // 清除选择
                dataGridView_投注记录.ClearSelection();

                // 更新统计信息
                UpdateBetRecordStatistics();

                _logger.LogInformation(@"投注记录查询完成，共 {Count} 条记录", _betRecordViewModels.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询投注记录失败");
            MessageBox.Show($@"查询投注记录失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 示例：查询今日投注记录
    /// 可以绑定到查询按钮的Click事件
    /// </summary>
    public async void QueryTodayBetRecords()
    {
        var startTime = DateTime.Today;
        var endTime = DateTime.Today.AddDays(1).AddSeconds(-1);
        await QueryBetRecordsAsync(startTime, endTime, includeFakeUsers: true);
    }

    /// <summary>
    /// 示例：查询指定条件的投注记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="issue">期号（可选）</param>
    /// <param name="account">账号（可选）</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    public async void QueryBetRecordsWithConditions(DateTime startTime, DateTime endTime, string? issue = null, string? account = null, bool includeFakeUsers = false)
    {
        await QueryBetRecordsAsync(startTime, endTime, issue, account, includeFakeUsers);
    }

    /// <summary>
    /// 快捷查询：查询昨天的投注记录
    /// </summary>
    public async void QueryYesterdayBetRecords()
    {
        var yesterday = DateTime.Today.AddDays(-1);
        await QueryBetRecordsAsync(yesterday, yesterday.AddDays(1).AddSeconds(-1), includeFakeUsers: true);
    }

    /// <summary>
    /// 快捷查询：查询本周的投注记录
    /// </summary>
    public async void QueryThisWeekBetRecords()
    {
        var today = DateTime.Today;
        var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
        await QueryBetRecordsAsync(startOfWeek, today.AddDays(1).AddSeconds(-1), includeFakeUsers: true);
    }

    /// <summary>
    /// 快捷查询：查询指定用户的投注记录
    /// </summary>
    /// <param name="account">用户账号</param>
    public async void QueryUserBetRecords(string account)
    {
        var today = DateTime.Today;
        await QueryBetRecordsAsync(today.AddDays(-7), today.AddDays(1).AddSeconds(-1), account: account, includeFakeUsers: true);
    }

    /// <summary>
    /// 更新投注记录统计信息
    /// </summary>
    private void UpdateBetRecordStatistics()
    {
        try
        {
            var (validBetAmount, totalProfitLoss) = _betRecordService.GetBetRecordStatistics(_betRecordViewModels);

            // 更新标签显示
            label_显示查询投注记录有效下注总额以及总盈亏.Text = $@"有效下注总额: {validBetAmount:F2} | 总盈亏: {totalProfitLoss:F2}";

            _logger.LogDebug(@"投注记录统计更新 - 有效投注总额: {ValidBetAmount:F2}, 总盈亏: {TotalProfitLoss:F2}",
                validBetAmount, totalProfitLoss);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新投注记录统计信息失败");
            label_显示查询投注记录有效下注总额以及总盈亏.Text = @"统计信息加载失败";
        }
    }

    #endregion
}
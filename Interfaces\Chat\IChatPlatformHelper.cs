namespace CommandGuard.Interfaces.Chat;

/// <summary>
/// 聊天平台助手接口 - 统一的聊天平台操作接口
/// 提供跨平台的聊天功能抽象，支持MyQQ、一起聊吧等多个平台
/// 包含机器人信息获取、群组管理、昵称获取、消息发送等核心功能
/// </summary>
public interface IChatPlatformHelper
{
    /// <summary>
    /// 获取机器人信息
    /// 包括机器人账号、昵称等基本信息
    /// </summary>
    /// <returns>异步任务</returns>
    Task GetRobotInfoAsync();

    /// <summary>
    /// 获取群组字典信息
    /// 获取机器人所在的群组列表和相关信息
    /// </summary>
    /// <returns>异步任务</returns>
    Task GetGroupDicAsync();

    /// <summary>
    /// 获取用户昵称
    /// 根据用户账号获取其在平台上的昵称
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>用户昵称，获取失败时返回空字符串</returns>
    Task<string> GetNickNameAsync(string account);

    /// <summary>
    /// 发送群组消息
    /// 向指定群组发送文本消息
    /// </summary>
    /// <param name="message">要发送的消息内容</param>
    /// <returns>异步任务</returns>
    Task SendGroupMessageAsync(string message);

    /// <summary>
    /// 发送群组消息并@指定用户
    /// 向指定群组发送文本消息，并@指定的用户
    /// </summary>
    /// <param name="message">要发送的消息内容</param>
    /// <param name="atAccount">要@的用户账号</param>
    /// <returns>异步任务</returns>
    Task SendGroupMessageAsync(string message, string atAccount);

    /// <summary>
    /// 发送图片消息
    /// 向群组发送图片文件
    /// </summary>
    /// <param name="imgPath">图片文件的完整路径</param>
    /// <returns>异步任务</returns>
    Task SendImageAsync(string imgPath);

    /// <summary>
    /// 获取平台名称
    /// 返回当前聊天平台的名称标识
    /// </summary>
    /// <returns>平台名称</returns>
    string GetPlatformName();
}
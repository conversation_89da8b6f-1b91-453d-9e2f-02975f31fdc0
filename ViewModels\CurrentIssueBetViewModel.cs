using CommandGuard.Enums;

namespace CommandGuard.ViewModels;

/// <summary>
/// 当前期投注数据视图模型
/// 用于在dataGridView4中显示当前期的投注数据
/// </summary>
public class CurrentIssueBetViewModel
{
    /// <summary>
    /// 订单ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 期号
    /// </summary>
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 账号
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 类型（投注项目）
    /// </summary>
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 积分（投注金额）
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 飞单状态
    /// </summary>
    public EnumFlightOrderStatus EnumFlightStatus { get; set; }

    /// <summary>
    /// 投注时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 飞单时间
    /// </summary>
    public DateTime? FlightTime { get; set; }

    /// <summary>
    /// 订单真假状态
    /// true: 真订单（用户在创建时为真人状态）
    /// false: 假订单（用户在创建时为假人状态）
    /// </summary>
    public bool IsRealOrder { get; set; } = true;

    /// <summary>
    /// 获取用户显示名称
    /// 优先显示昵称，如果昵称为空则显示账号
    /// </summary>
    /// <returns>用户显示名称</returns>
    public string GetDisplayName()
    {
        return string.IsNullOrWhiteSpace(NickName) ? Account : NickName;
    }

    /// <summary>
    /// 获取飞单状态描述
    /// </summary>
    /// <returns>飞单状态的中文描述</returns>
    public string GetFlightStatusDescription()
    {
        return EnumFlightStatus.GetDescription();
    }

    /// <summary>
    /// 获取飞单状态颜色
    /// </summary>
    /// <returns>飞单状态的颜色代码</returns>
    public string GetFlightStatusColor()
    {
        return EnumFlightStatus.GetColorCode();
    }

    /// <summary>
    /// 获取格式化的投注金额
    /// </summary>
    /// <returns>格式化的金额字符串</returns>
    public string GetFormattedAmount()
    {
        return Amount.ToString(@"F2");
    }

    /// <summary>
    /// 获取格式化的投注时间
    /// </summary>
    /// <returns>格式化的时间字符串</returns>
    public string GetFormattedCreatedTime()
    {
        return CreatedTime.ToString(@"HH:mm:ss");
    }

    /// <summary>
    /// 获取格式化的飞单时间
    /// </summary>
    /// <returns>格式化的时间字符串</returns>
    public string GetFormattedFlightTime()
    {
        return FlightTime?.ToString(@"HH:mm:ss") ?? @"-";
    }

    /// <summary>
    /// 获取投注详情描述
    /// </summary>
    /// <returns>投注详情字符串</returns>
    public string GetBetDescription()
    {
        return $@"{PlayItem}/{GetFormattedAmount()}";
    }

    /// <summary>
    /// 判断是否可以飞单
    /// </summary>
    /// <returns>是否可以飞单</returns>
    public bool CanFlight()
    {
        return EnumFlightStatus == EnumFlightOrderStatus.Pending || EnumFlightStatus == EnumFlightOrderStatus.Failed;
    }

    /// <summary>
    /// 判断是否正在飞单
    /// </summary>
    /// <returns>是否正在飞单</returns>
    public bool IsFlighting()
    {
        return EnumFlightStatus == EnumFlightOrderStatus.Processing;
    }

    /// <summary>
    /// 判断飞单是否完成
    /// </summary>
    /// <returns>飞单是否完成</returns>
    public bool IsFlightCompleted()
    {
        return EnumFlightStatus.IsFinalStatus();
    }

    /// <summary>
    /// 判断飞单是否成功
    /// </summary>
    /// <returns>飞单是否成功</returns>
    public bool IsFlightSuccess()
    {
        return EnumFlightStatus == EnumFlightOrderStatus.Success;
    }

    /// <summary>
    /// 获取状态图标
    /// </summary>
    /// <returns>状态图标字符串</returns>
    public string GetStatusIcon()
    {
        return EnumFlightStatus switch
        {
            EnumFlightOrderStatus.Pending => @"⏳",
            EnumFlightOrderStatus.Processing => @"🔄",
            EnumFlightOrderStatus.Success => @"✅",
            EnumFlightOrderStatus.Failed => @"❌",
            _ => @"❓"
        };
    }

    /// <summary>
    /// 获取完整的状态显示文本
    /// </summary>
    /// <returns>包含图标的状态文本</returns>
    public string GetFullStatusText()
    {
        return $@"{GetStatusIcon()} {GetFlightStatusDescription()}";
    }
}

using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces.Lottery;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 彩种选择窗体
/// 提供用户友好的彩种选择界面
/// </summary>
public partial class FormLotterySelector : Form
{
    #region 私有字段

    private readonly ILogger<FormLotterySelector> _logger;
    private readonly ILotteryConfigurationService _lotteryConfig;
    private EnumLottery _selectedLottery;
    private bool _isInitializing = true;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public FormLotterySelector(
        ILogger<FormLotterySelector> logger,
        ILotteryConfigurationService lotteryConfig)
    {
        _logger = logger;
        _lotteryConfig = lotteryConfig;
        
        InitializeComponent();
        InitializeFormAsync();
    }

    #endregion

    #region 公共属性

    /// <summary>
    /// 当前选择的彩种
    /// </summary>
    public EnumLottery SelectedLottery => _selectedLottery;

    #endregion

    #region 初始化方法

    /// <summary>
    /// 异步初始化窗体
    /// </summary>
    private async void InitializeFormAsync()
    {
        try
        {
            _isInitializing = true;

            // 获取当前彩种
            _selectedLottery = await _lotteryConfig.GetCurrentLotteryAsync();

            // 初始化控件
            InitializeLotteryComboBox();
            InitializeLotteryInfo();

            _isInitializing = false;
            _logger.LogInformation(@"彩种选择窗体初始化完成，当前彩种: {Lottery}", _selectedLottery);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化彩种选择窗体失败");
            MessageBox.Show(@"初始化失败，请重试", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 初始化彩种下拉框
    /// </summary>
    private void InitializeLotteryComboBox()
    {
        comboBoxLottery.Items.Clear();
        
        var lotteries = LotteryHelper.GetSupportedLotteries();
        foreach (var lottery in lotteries)
        {
            var info = LotteryHelper.LotteryInfos[lottery];
            comboBoxLottery.Items.Add(new LotteryItem(lottery, info.DisplayName));
        }

        // 设置当前选择
        for (int i = 0; i < comboBoxLottery.Items.Count; i++)
        {
            if (comboBoxLottery.Items[i] is LotteryItem item && item.Lottery == _selectedLottery)
            {
                comboBoxLottery.SelectedIndex = i;
                break;
            }
        }
    }

    /// <summary>
    /// 初始化彩种信息显示
    /// </summary>
    private void InitializeLotteryInfo()
    {
        UpdateLotteryInfo(_selectedLottery);
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 彩种选择变更事件
    /// </summary>
    private void ComboBoxLottery_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (_isInitializing) return;

        try
        {
            if (comboBoxLottery.SelectedItem is LotteryItem selectedItem)
            {
                _selectedLottery = selectedItem.Lottery;
                UpdateLotteryInfo(_selectedLottery);
                _logger.LogDebug(@"彩种选择已更新: {Lottery}", _selectedLottery);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理彩种选择变更事件失败");
        }
    }

    /// <summary>
    /// 确定按钮点击事件
    /// </summary>
    private async void ButtonOK_Click(object sender, EventArgs e)
    {
        try
        {
            // 保存彩种选择
            await _lotteryConfig.SetCurrentLotteryAsync(_selectedLottery);
            
            DialogResult = DialogResult.OK;
            Close();
            
            _logger.LogInformation(@"彩种选择已保存: {Lottery}", _selectedLottery);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"保存彩种选择失败");
            MessageBox.Show(@"保存失败，请重试", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    private void ButtonCancel_Click(object sender, EventArgs e)
    {
        DialogResult = DialogResult.Cancel;
        Close();
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 更新彩种信息显示
    /// </summary>
    private void UpdateLotteryInfo(EnumLottery lottery)
    {
        var info = LotteryHelper.LotteryInfos[lottery];
        
        labelDescription.Text = info.Description;
        labelCalculationMethod.Text = $@"计算方法：{info.CalculationMethod}";
        
        // 更新示例说明
        var exampleText = lottery switch
        {
            EnumLottery.宾果1 => @"示例：如果第21个号码是13，则番摊结果为 13 ÷ 4 = 余数1，结果为1",
            EnumLottery.宾果2 => @"示例：如果第1、20、21个号码分别是5、8、7，则番摊结果为 (5+8+7) ÷ 4 = 余数0，结果为4",
            EnumLottery.宾果3 => @"示例：如果前20个号码之和是85，则番摊结果为 85 ÷ 4 = 余数1，结果为1",
            _ => @""
        };
        
        labelExample.Text = exampleText;
    }

    #endregion

    #region 内部类

    /// <summary>
    /// 彩种下拉框项目
    /// </summary>
    private class LotteryItem(EnumLottery lottery, string displayName)
    {
        public EnumLottery Lottery { get; } = lottery;
        public string DisplayName { get; } = displayName;

        public override string ToString() => DisplayName;
    }

    #endregion
}

using CommandGuard.Enums;
using CommandGuard.Services.Lottery;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Helpers;

/// <summary>
/// 彩种测试辅助类
/// 用于测试和验证多彩种功能的正确性
/// </summary>
public static class LotteryTestHelper
{
    /// <summary>
    /// 测试所有彩种的番摊计算
    /// </summary>
    /// <param name="settlementService">结算服务</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>测试结果</returns>
    public static async Task<bool> TestAllLotteryCalculationsAsync(
        SettlementService settlementService,
        ILogger logger)
    {
        try
        {
            logger.LogInformation(@"开始测试所有彩种的番摊计算...");

            // 测试用的开奖号码（21个号码）
            var testNumbers = @"01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21";
            
            // 解析号码用于验证
            var numbers = testNumbers.Split(',').Select(int.Parse).ToArray();
            
            // 测试宾果1：第21个号码除以4
            var bingo1Result = await settlementService.CalculateFanTanResultAsync(testNumbers, EnumLottery.宾果1);
            var expectedBingo1 = numbers[20] % 4; // 第21个号码（索引20）
            if (expectedBingo1 == 0) expectedBingo1 = 4;
            
            logger.LogInformation(@"宾果1测试 - 第21个号码: {Number}, 计算结果: {Result}, 期望结果: {Expected}",
                numbers[20], bingo1Result, expectedBingo1);
            
            if (bingo1Result != expectedBingo1)
            {
                logger.LogError(@"宾果1计算错误！");
                return false;
            }

            // 测试宾果2：第1+第20+第21个号码之和除以4
            var bingo2Result = await settlementService.CalculateFanTanResultAsync(testNumbers, EnumLottery.宾果2);
            var sum2 = numbers[0] + numbers[19] + numbers[20]; // 第1、20、21个号码
            var expectedBingo2 = sum2 % 4;
            if (expectedBingo2 == 0) expectedBingo2 = 4;
            
            logger.LogInformation(@"宾果2测试 - 第1+20+21个号码之和: {Sum}, 计算结果: {Result}, 期望结果: {Expected}",
                sum2, bingo2Result, expectedBingo2);
            
            if (bingo2Result != expectedBingo2)
            {
                logger.LogError(@"宾果2计算错误！");
                return false;
            }

            // 测试宾果3：前20个号码之和除以4
            var bingo3Result = await settlementService.CalculateFanTanResultAsync(testNumbers, EnumLottery.宾果3);
            var sum3 = numbers.Take(20).Sum(); // 前20个号码之和
            var expectedBingo3 = sum3 % 4;
            if (expectedBingo3 == 0) expectedBingo3 = 4;
            
            logger.LogInformation(@"宾果3测试 - 前20个号码之和: {Sum}, 计算结果: {Result}, 期望结果: {Expected}",
                sum3, bingo3Result, expectedBingo3);
            
            if (bingo3Result != expectedBingo3)
            {
                logger.LogError(@"宾果3计算错误！");
                return false;
            }

            logger.LogInformation(@"所有彩种番摊计算测试通过！");
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"测试彩种番摊计算时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 生成测试报告
    /// </summary>
    /// <param name="settlementService">结算服务</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>测试报告</returns>
    public static async Task<string> GenerateTestReportAsync(
        SettlementService settlementService,
        ILogger logger)
    {
        var report = new System.Text.StringBuilder();
        report.AppendLine(@"=== 多彩种功能测试报告 ===");
        report.AppendLine($@"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine();

        try
        {
            // 测试多组不同的号码
            var testCases = new[]
            {
                @"01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21",
                @"05,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,01,02,03,04,05",
                @"80,79,78,77,76,75,74,73,72,71,70,69,68,67,66,65,64,63,62,61,60"
            };

            for (int i = 0; i < testCases.Length; i++)
            {
                var testNumbers = testCases[i];
                report.AppendLine($@"测试用例 {i + 1}: {testNumbers}");
                
                var numbers = testNumbers.Split(',').Select(int.Parse).ToArray();

                // 测试每种彩种
                foreach (var lottery in LotteryHelper.GetSupportedLotteries())
                {
                    var result = await settlementService.CalculateFanTanResultAsync(testNumbers, lottery);
                    var info = LotteryHelper.LotteryInfos[lottery];
                    
                    report.AppendLine($@"  {info.DisplayName}: 番摊结果 = {result}");
                    
                    // 添加计算详情
                    switch (lottery)
                    {
                        case EnumLottery.宾果1:
                            report.AppendLine($@"    计算: 第21个号码({numbers[20]}) ÷ 4 = 余数{numbers[20] % 4} → {result}");
                            break;
                        case EnumLottery.宾果2:
                            var sum2 = numbers[0] + numbers[19] + numbers[20];
                            report.AppendLine($@"    计算: ({numbers[0]}+{numbers[19]}+{numbers[20]}) ÷ 4 = {sum2} ÷ 4 = 余数{sum2 % 4} → {result}");
                            break;
                        case EnumLottery.宾果3:
                            var sum3 = numbers.Take(20).Sum();
                            report.AppendLine($@"    计算: 前20个号码之和({sum3}) ÷ 4 = 余数{sum3 % 4} → {result}");
                            break;
                    }
                }
                
                report.AppendLine();
            }

            // 测试兼容性方法
            report.AppendLine(@"=== 兼容性测试 ===");
            var compatResult = settlementService.CalculateFanTanResult(testCases[0]);
            var asyncResult = await settlementService.CalculateFanTanResultAsync(testCases[0], EnumLottery.宾果3);
            
            report.AppendLine($@"兼容性方法结果: {compatResult}");
            report.AppendLine($@"异步方法结果: {asyncResult}");
            report.AppendLine($@"结果一致性: {(compatResult == asyncResult ? "通过" : "失败")}");

            report.AppendLine();
            report.AppendLine(@"=== 测试完成 ===");
            
            logger.LogInformation(@"测试报告生成完成");
        }
        catch (Exception ex)
        {
            report.AppendLine($@"测试过程中发生异常: {ex.Message}");
            logger.LogError(ex, @"生成测试报告时发生异常");
        }

        return report.ToString();
    }

    /// <summary>
    /// 验证号码数量要求
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <returns>验证结果</returns>
    public static bool ValidateNumberRequirements(ILogger logger)
    {
        try
        {
            logger.LogInformation(@"开始验证号码数量要求...");

            foreach (var lottery in LotteryHelper.GetSupportedLotteries())
            {
                var requiredCount = LotteryHelper.GetRequiredNumberCount(lottery);
                var info = LotteryHelper.LotteryInfos[lottery];
                
                logger.LogInformation(@"{Lottery} 需要 {Count} 个号码", info.DisplayName, requiredCount);
                
                // 测试边界情况
                var validCount = LotteryHelper.ValidateNumberCount(lottery, requiredCount);
                var invalidCount = LotteryHelper.ValidateNumberCount(lottery, requiredCount - 1);
                
                if (!validCount || invalidCount)
                {
                    logger.LogError(@"{Lottery} 号码数量验证失败", info.DisplayName);
                    return false;
                }
            }

            logger.LogInformation(@"号码数量要求验证通过");
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"验证号码数量要求时发生异常");
            return false;
        }
    }
}

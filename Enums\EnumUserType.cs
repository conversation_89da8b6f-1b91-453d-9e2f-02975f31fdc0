﻿namespace CommandGuard.Enums;

/// <summary>
/// 用户类型枚举
/// 定义系统中用户的类型分类
/// </summary>
public enum EnumUserType
{
    /// <summary>
    /// 真人用户 - 真实的用户
    /// </summary>
    Real = 1,

    /// <summary>
    /// 假人用户 - 系统模拟用户，用于测试或演示
    /// </summary>
    Fake = 2
}

/// <summary>
/// 用户类型枚举扩展方法
/// </summary>
public static class UserTypeExtensions
{
    /// <summary>
    /// 获取用户类型的中文描述
    /// </summary>
    /// <param name="userType">用户类型</param>
    /// <returns>中文描述</returns>
    public static string GetDescription(this EnumUserType userType)
    {
        return userType switch
        {
            EnumUserType.Real => @"真人",
            EnumUserType.Fake => @"假人",
            _ => @"未知"
        };
    }

    /// <summary>
    /// 从字符串转换为用户类型枚举
    /// </summary>
    /// <param name="userTypeString">用户类型字符串</param>
    /// <returns>用户类型枚举</returns>
    public static EnumUserType FromString(string userTypeString)
    {
        return userTypeString?.Trim() switch
        {
            @"真人" => EnumUserType.Real,
            @"假人" => EnumUserType.Fake,
            _ => EnumUserType.Real // 默认为真人
        };
    }

    /// <summary>
    /// 判断是否为真人用户
    /// </summary>
    /// <param name="userType">用户类型</param>
    /// <returns>是否为真人用户</returns>
    public static bool IsReal(this EnumUserType userType)
    {
        return userType == EnumUserType.Real;
    }

    /// <summary>
    /// 判断是否为假人用户
    /// </summary>
    /// <param name="userType">用户类型</param>
    /// <returns>是否为假人用户</returns>
    public static bool IsFake(this EnumUserType userType)
    {
        return userType == EnumUserType.Fake;
    }
}

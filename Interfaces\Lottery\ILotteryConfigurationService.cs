using CommandGuard.Enums;

namespace CommandGuard.Interfaces.Lottery;

/// <summary>
/// 台湾宾果3配置服务 - 提供台湾宾果3游戏的配置信息
/// 简化的配置服务，专门为台湾宾果3游戏提供固定的配置参数
/// </summary>
public interface ILotteryConfigurationService
{
    /// <summary>
    /// 当前彩票游戏类型（固定为台湾宾果）
    /// </summary>
    EnumLottery CurrentLottery { get; }

    /// <summary>
    /// 获取空开奖号码字符串
    /// </summary>
    /// <returns>台湾宾果的空号码字符串（21个0）</returns>
    string GetEmptyDrawNumbers();

    /// <summary>
    /// 获取最大数据条数
    /// </summary>
    /// <returns>台湾宾果的最大数据条数（20）</returns>
    int GetMaxDataCount();

    /// <summary>
    /// 获取数据查询标识
    /// </summary>
    /// <param name="kaiJiang">开奖数据</param>
    /// <returns>用于数据库查询的标识字符串（时间前10位）</returns>
    string GetDataQueryFlag(Models.KaiJiang kaiJiang);

    /// <summary>
    /// 判断是否使用时间查询
    /// </summary>
    /// <returns>固定返回true（台湾宾果使用时间查询）</returns>
    bool UseTimeQuery();
}
using CommandGuard.Enums;

namespace CommandGuard.Interfaces.Lottery;

/// <summary>
/// 彩票配置服务 - 提供多彩种游戏的配置信息
/// 支持宾果1、宾果2、宾果3三种彩种的动态配置
/// 根据当前选择的彩种提供对应的配置参数
/// </summary>
public interface ILotteryConfigurationService
{
    /// <summary>
    /// 当前彩票游戏类型
    /// 从系统设置中动态获取当前选择的彩种
    /// </summary>
    Task<EnumLottery> GetCurrentLotteryAsync();

    /// <summary>
    /// 设置当前彩票游戏类型
    /// </summary>
    /// <param name="lottery">要设置的彩种</param>
    Task SetCurrentLotteryAsync(EnumLottery lottery);

    /// <summary>
    /// 获取指定彩种的空开奖号码字符串
    /// </summary>
    /// <param name="lottery">彩种类型，如果为null则使用当前彩种</param>
    /// <returns>对应彩种的空号码字符串（21个0）</returns>
    Task<string> GetEmptyDrawNumbersAsync(EnumLottery? lottery = null);

    /// <summary>
    /// 获取指定彩种的最大数据条数
    /// </summary>
    /// <param name="lottery">彩种类型，如果为null则使用当前彩种</param>
    /// <returns>对应彩种的最大数据条数（20）</returns>
    Task<int> GetMaxDataCountAsync(EnumLottery? lottery = null);

    /// <summary>
    /// 获取指定彩种的数据查询标识
    /// </summary>
    /// <param name="kaiJiang">开奖数据</param>
    /// <param name="lottery">彩种类型，如果为null则使用当前彩种</param>
    /// <returns>用于数据库查询的标识字符串（时间前10位）</returns>
    Task<string> GetDataQueryFlagAsync(Models.KaiJiang kaiJiang, EnumLottery? lottery = null);

    /// <summary>
    /// 判断指定彩种是否使用时间查询
    /// </summary>
    /// <param name="lottery">彩种类型，如果为null则使用当前彩种</param>
    /// <returns>是否使用时间查询（所有宾果彩种都返回true）</returns>
    Task<bool> UseTimeQueryAsync(EnumLottery? lottery = null);

    /// <summary>
    /// 获取彩种的显示名称
    /// </summary>
    /// <param name="lottery">彩种类型</param>
    /// <returns>彩种的显示名称</returns>
    string GetLotteryDisplayName(EnumLottery lottery);

    /// <summary>
    /// 获取所有支持的彩种列表
    /// </summary>
    /// <returns>支持的彩种列表</returns>
    List<EnumLottery> GetSupportedLotteries();

    #region 兼容性方法（保持向后兼容）

    /// <summary>
    /// 获取空开奖号码字符串（兼容性方法）
    /// </summary>
    string GetEmptyDrawNumbers();

    /// <summary>
    /// 获取最大数据条数（兼容性方法）
    /// </summary>
    int GetMaxDataCount();

    /// <summary>
    /// 获取数据查询标识（兼容性方法）
    /// </summary>
    string GetDataQueryFlag(Models.KaiJiang kaiJiang);

    /// <summary>
    /// 判断是否使用时间查询（兼容性方法）
    /// </summary>
    bool UseTimeQuery();

    #endregion
}
using System.ComponentModel;

namespace CommandGuard.ViewModels;

/// <summary>
/// 拉手返点记录视图模型
/// 用于在拉手返点查询界面显示拉手的流水和返点数据
/// </summary>
public class AgentRebateRecordViewModel : INotifyPropertyChanged
{
    private string _nickName = string.Empty;
    private string _account = string.Empty;
    private decimal _validTurnover;
    private decimal _agentRebatePercent;
    private decimal _rebateAmount;

    /// <summary>
    /// 昵称
    /// 拉手的昵称
    /// </summary>
    public string NickName
    {
        get => _nickName;
        set
        {
            _nickName = value ?? string.Empty;
            OnPropertyChanged(nameof(NickName));
        }
    }

    /// <summary>
    /// 账号
    /// 拉手的账号
    /// </summary>
    public string Account
    {
        get => _account;
        set
        {
            _account = value ?? string.Empty;
            OnPropertyChanged(nameof(Account));
        }
    }

    /// <summary>
    /// 当天流水
    /// 拉手名下会员在查询时间范围内的有效投注总额（输或赢的投注额，不含和局的）
    /// </summary>
    public decimal ValidTurnover
    {
        get => _validTurnover;
        set
        {
            _validTurnover = value;
            OnPropertyChanged(nameof(ValidTurnover));
            OnPropertyChanged(nameof(ValidTurnoverFormatted));
            // 当流水变化时，重新计算返水金额
            CalculateRebateAmount();
        }
    }

    /// <summary>
    /// 拉手返点比例
    /// 拉手的返点比例（百分比）
    /// </summary>
    public decimal AgentRebatePercent
    {
        get => _agentRebatePercent;
        set
        {
            _agentRebatePercent = value;
            OnPropertyChanged(nameof(AgentRebatePercent));
            OnPropertyChanged(nameof(AgentRebatePercentFormatted));
            // 当返点比例变化时，重新计算返水金额
            CalculateRebateAmount();
        }
    }

    /// <summary>
    /// 返水金额
    /// 有效流水乘以拉手返点比例后的金额
    /// </summary>
    public decimal RebateAmount
    {
        get => _rebateAmount;
        private set
        {
            _rebateAmount = value;
            OnPropertyChanged(nameof(RebateAmount));
            OnPropertyChanged(nameof(RebateAmountFormatted));
        }
    }

    /// <summary>
    /// 操作
    /// 预留的操作列，暂时留空
    /// </summary>
    public string Operation { get; set; } = string.Empty;

    #region 格式化属性

    /// <summary>
    /// 格式化的有效流水
    /// </summary>
    public string ValidTurnoverFormatted => ValidTurnover.ToString(@"F2");

    /// <summary>
    /// 格式化的拉手返点比例（不显示百分号）
    /// </summary>
    public string AgentRebatePercentFormatted => AgentRebatePercent.ToString(@"F2");

    /// <summary>
    /// 格式化的返水金额
    /// </summary>
    public string RebateAmountFormatted => RebateAmount.ToString(@"F2");

    #endregion

    #region 业务方法

    /// <summary>
    /// 计算返水金额
    /// 返水金额 = 有效流水 × 拉手返点比例 ÷ 100
    /// </summary>
    private void CalculateRebateAmount()
    {
        RebateAmount = ValidTurnover * (AgentRebatePercent / 100);
    }

    /// <summary>
    /// 获取拉手返点记录摘要
    /// </summary>
    /// <returns>拉手返点记录摘要字符串</returns>
    public string GetSummary()
    {
        return $@"拉手[{Account}({NickName})] - 流水:{ValidTurnoverFormatted}, 返点:{AgentRebatePercentFormatted}, 返水:{RebateAmountFormatted}";
    }

    #endregion

    #region INotifyPropertyChanged 实现

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    #endregion
}

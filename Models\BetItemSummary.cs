namespace CommandGuard.Models;

/// <summary>
/// 投注项目汇总信息
/// 用于表示按投注项目分组后的汇总数据
/// </summary>
public class BetItemSummary
{
    /// <summary>
    /// 投注项目（如：大、小、单、双、1番、2番等）
    /// </summary>
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 该投注项目的总投注金额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 该投注项目的投注次数
    /// </summary>
    public int BetCount { get; set; }

    /// <summary>
    /// 该投注项目的平均投注金额
    /// </summary>
    public decimal AverageAmount => BetCount > 0 ? TotalAmount / BetCount : 0;

    /// <summary>
    /// 格式化显示文本（如：大/100）
    /// </summary>
    public string DisplayText => $"{PlayItem}/{TotalAmount:F0}";

    /// <summary>
    /// 构造函数
    /// </summary>
    public BetItemSummary()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="playItem">投注项目</param>
    /// <param name="totalAmount">总投注金额</param>
    /// <param name="betCount">投注次数</param>
    public BetItemSummary(string playItem, decimal totalAmount, int betCount = 1)
    {
        PlayItem = playItem;
        TotalAmount = totalAmount;
        BetCount = betCount;
    }
}

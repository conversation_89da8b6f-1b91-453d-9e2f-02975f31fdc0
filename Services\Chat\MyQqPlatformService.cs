using CommandGuard.Configuration;
using CommandGuard.Interfaces.Chat;
using Flurl.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CommandGuard.Services.Chat;

/// <summary>
/// MyQQ平台服务实现 - MyQQ聊天平台的具体实现
/// 提供MyQQ平台的机器人操作、消息发送、用户信息获取等功能
/// 基于HTTP API与MyQQ客户端进行通信
/// </summary>
public class MyQqPlatformService(ILogger<MyQqPlatformService> logger) : IChatPlatformHelper
{
    /// <summary>
    /// MyQQ API主机地址
    /// </summary>
    private static string Host => @"http://127.0.0.1:8899/MyQQHTTPAPI";

    /// <summary>
    /// MyQQ API访问令牌
    /// </summary>
    private static string Token => @"666";

    /// <summary>
    /// 获取平台名称
    /// </summary>
    public string GetPlatformName() => "MyQQ";

    /// <summary>
    /// 获取机器人信息
    /// </summary>
    public async Task GetRobotInfoAsync()
    {
        try
        {
            // 构建Json对象 - 与MyQqHelper.cs完全一致
            JObject json = new JObject
            {
                ["function"] = "Api_GetOnlineQQlist",
                ["token"] = Token
            };

            // 发送HTTP请求 - 与MyQqHelper.cs完全一致
            string response = await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString())
                .ReceiveString();

            // 解析响应并更新机器人信息
            if (!string.IsNullOrWhiteSpace(response) && response.Contains("成功"))
            {
                JObject jObj = JObject.Parse(response);

                var account = jObj["data"]?["ret"]?.ToString()!;
                RuntimeConfiguration.UpdateRobotAccount(account);
                var nickName = await GetNickNameAsync(account);
                RuntimeConfiguration.UpdateRobotNickName(nickName);

                logger.LogInformation(@"MyQQ机器人信息获取成功，账号: {Account}, 昵称: {NickName}",
                    RuntimeConfiguration.GetRobotAccount(), RuntimeConfiguration.GetRobotNickName());
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取MyQQ机器人信息失败");
        }
    }

    /// <summary>
    /// 获取群组字典信息
    /// </summary>
    public async Task GetGroupDicAsync()
    {
        try
        {
            // 构建Json对象 - 与MyQqHelper.cs完全一致
            JObject json = new JObject
            {
                ["function"] = "Api_GetGroupList_A",
                ["token"] = Token,
                ["params"] = new JObject
                {
                    ["c1"] = RuntimeConfiguration.GetRobotAccount()
                }
            };

            // 发送HTTP请求 - 与MyQqHelper.cs完全一致
            string response = await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString())
                .ReceiveString();

            // 解析响应并更新群组信息
            if (!string.IsNullOrWhiteSpace(response) && response.Contains("成功"))
            {
                JObject jObj = JObject.Parse(response);
                string resultData = jObj["data"]?["ret"]?.ToString()!;
                string[] groupIdLines = resultData.Split(["\r\n"], StringSplitOptions.RemoveEmptyEntries);

                foreach (string groupId in groupIdLines)
                {
                    string groupName = await GetGroupNameAsync(RuntimeConfiguration.GetRobotAccount(), groupId);
                    RuntimeConfiguration.AddOrUpdateGroup(groupId, groupName);
                }

                logger.LogInformation(@"MyQQ群组信息获取成功，共获取 {Count} 个群组", groupIdLines.Length);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取MyQQ群组信息失败");
        }
    }

    /// <summary>
    /// 获取群组名称 - 与MyQqHelper.cs中的GetGroupName方法完全一致
    /// </summary>
    /// <param name="robotId">机器人ID</param>
    /// <param name="groupId">群组ID</param>
    /// <returns>群组名称</returns>
    private async Task<string> GetGroupNameAsync(string robotId, string groupId)
    {
        string groupName = "";
        try
        {
            // 构建Json对象 - 与MyQqHelper.cs完全一致
            JObject json = new JObject
            {
                ["function"] = "Api_GetGroupName",
                ["token"] = Token,
                ["params"] = new JObject
                {
                    ["c1"] = $"{robotId}",
                    ["c2"] = $"{groupId}"
                }
            };

            // 发送HTTP请求 - 与MyQqHelper.cs完全一致
            string response = await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString())
                .ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("成功"))
            {
                JObject jObj = JObject.Parse(response);
                string resultData = jObj["data"]?["ret"]?.ToString()!;
                groupName = resultData;
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, @"获取群组名称失败，群组ID: {GroupId}", groupId);
        }

        return groupName;
    }

    /// <summary>
    /// 获取用户昵称 - 与MyQqHelper.cs完全一致
    /// </summary>
    /// <param name="account">QQ号</param>
    /// <returns>用户昵称</returns>
    public async Task<string> GetNickNameAsync(string account)
    {
        string nickName = "";
        try
        {
            // 构建Json对象 - 与MyQqHelper.cs完全一致
            JObject json = new JObject
            {
                ["function"] = "Api_GetNick",
                ["token"] = Token,
                ["params"] = new JObject
                {
                    ["c1"] = $"{RuntimeConfiguration.GetRobotAccount()}",
                    ["c2"] = $"{account}"
                }
            };

            // 发送HTTP请求 - 与MyQqHelper.cs完全一致
            string response = await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString())
                .ReceiveString();

            if (!string.IsNullOrWhiteSpace(response) && response.Contains("成功"))
            {
                JObject jObj = JObject.Parse(response);
                string resultData = jObj["data"]?["ret"]?.ToString()!;
                nickName = resultData;

                logger.LogDebug(@"MyQQ获取昵称成功，账号: {Account}, 昵称: {NickName}", account, nickName);
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, @"MyQQ获取昵称失败，账号: {Account}", account);
            nickName = account; // 失败时使用账号作为昵称
        }

        return nickName;
    }

    /// <summary>
    /// 发送群组消息 - 与MyQqHelper.cs完全一致
    /// </summary>
    /// <param name="message">消息内容</param>
    public async Task SendGroupMessageAsync(string message)
    {
        try
        {
            // 构建Json对象 - 与MyQqHelper.cs完全一致
            JObject json = new JObject
            {
                ["function"] = "Api_SendMsg",
                ["token"] = Token,
                ["params"] = new JObject
                {
                    ["c1"] = RuntimeConfiguration.GetRobotAccount(),
                    ["c2"] = "2",
                    ["c3"] = RuntimeConfiguration.GetWorkGroupId(),
                    ["c4"] = "",
                    ["c5"] = message
                }
            };

            // 发送HTTP请求 - 与MyQqHelper.cs完全一致
            await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString());

            logger.LogInformation(@"MyQQ群组消息发送成功: {Message}", message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"MyQQ发送群组消息失败: {Message}", message);
        }
    }

    /// <summary>
    /// 发送群组消息并@指定用户 - 与MyQqHelper.cs完全一致
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="atAccount">要@的用户账号</param>
    public async Task SendGroupMessageAsync(string message, string atAccount)
    {
        try
        {
            // 构建Json对象 - 与MyQqHelper.cs完全一致
            JObject json = new JObject
            {
                ["function"] = "Api_SendMsg",
                ["token"] = Token,
                ["params"] = new JObject
                {
                    ["c1"] = RuntimeConfiguration.GetRobotAccount(),
                    ["c2"] = "2",
                    ["c3"] = RuntimeConfiguration.GetWorkGroupId(),
                    ["c4"] = "",
                    ["c5"] = $"[@{atAccount}]" + message
                }
            };

            // 发送HTTP请求 - 与MyQqHelper.cs完全一致
            await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString());

            logger.LogInformation(@"MyQQ群组@消息发送成功: {Message}, @用户: {AtAccount}", message, atAccount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"MyQQ发送群组@消息失败: {Message}, @用户: {AtAccount}", message, atAccount);
        }
    }

    /// <summary>
    /// 发送图片消息 - 与MyQqHelper.cs完全一致
    /// </summary>
    /// <param name="imgPath">图片路径</param>
    public async Task SendImageAsync(string imgPath)
    {
        try
        {
            // 构建Json对象 - 与MyQqHelper.cs完全一致
            JObject json = new JObject
            {
                ["function"] = "Api_SendMsg",
                ["token"] = Token,
                ["params"] = new JObject
                {
                    ["c1"] = RuntimeConfiguration.GetRobotAccount(),
                    ["c2"] = "2",
                    ["c3"] = RuntimeConfiguration.GetWorkGroupId(), // 使用工作群组ID
                    ["c4"] = "",
                    ["c5"] = $"[pic={imgPath}]"
                }
            };

            // 发送HTTP请求 - 与MyQqHelper.cs完全一致
            await Host
                .WithTimeout(TimeSpan.FromSeconds(3)) // 设置超时时间为3秒
                .PostStringAsync(json.ToString());

            logger.LogInformation(@"MyQQ群组图片发送成功: {ImagePath}", imgPath);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"MyQQ发送群组图片失败: {ImagePath}", imgPath);
        }
    }
}
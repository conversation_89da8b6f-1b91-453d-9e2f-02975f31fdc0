﻿using System.Collections.Concurrent;

namespace CommandGuard.Models;

/// <summary>
/// 机器人信息模型类 - 存储当前机器人的基本身份信息
/// </summary>
public class RobotInfo
{
    /// <summary>
    /// 机器人账号 - 机器人在聊天平台上的唯一标识
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 机器人昵称 - 机器人在聊天平台上的显示名称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 工作群组ID - 机器人工作的主要群组标识
    /// </summary>
    public string WorkGroupId { get; set; } = string.Empty;

    /// <summary>
    /// 群组字典 - 存储群组ID和群组名称的映射关系
    /// </summary>
    public ConcurrentDictionary<string, string> GroupDic { get; set; } = new();
}
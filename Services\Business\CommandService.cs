using System.Text.RegularExpressions;
using AiHelper;
using CommandGuard.Configuration;
using CommandGuard.Constants;
using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using CommandGuard.Services.Infrastructure;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// Command服务实现
/// </summary>
public sealed class CommandService(
    ILogger<CommandService> logger,
    IFreeSql fSql,
    IDepositWithdrawRecordService depositWithdrawRecordService,
    IFinancialService financialService,
    IIssueTimeService issueTimeService,
    IBetCommandValidatorService betCommandValidatorService,
    IOddsService oddsService,
    IBetRecordService betRecordService,
    IMemberService memberService,
    IChatService chatService,
    ISystemSettingService systemSettingService,
    IAudioService audioService) : ICommandService
{
    #region 消息处理入口

    /// <summary>
    /// 处理消息指令 - 统一的消息处理入口
    ///
    /// 功能：
    /// - 统一处理各种指令类型
    /// - 支持数字指令、中文指令、英文指令
    /// - 正则表达式匹配复杂指令
    /// - 兜底处理未匹配指令为投注
    ///
    /// 支持的指令：
    /// - 0: 指令说明
    /// - 1/查/查分: 查询余额
    /// - 2/图/路: 发送路子图
    /// - 3: 发送开奖图
    /// - 4/回/回水/返水/反水/上水: 申请回水
    /// - 5/水/流水: 流水详情
    /// - 6/时间/封/开: 查询时间
    /// - 7/撤/撤销/取消: 撤销投注
    /// - 8: 发送摊图
    /// - 9: 发送结算报告
    /// - 上分指令: 上100/上分100/起100/起分100
    /// - 下分指令: 下100/下分100/落100/落分100
    /// - 其他: 投注指令
    /// </summary>
    /// <param name="message">包含指令的内部消息</param>
    public async Task ProcessMessageCommandAsync(InternalMessage message)
    {
        // 获取会员信息（此时会员应该已存在）
        var member = await memberService.GetMemberAsync(message.Account);
        if (member == null)
        {
            logger.LogWarning(@"会员不存在，账号: {Account}", message.Account);
            return;
        }

        // 处理换行符 - 统一文本格式
        string msgCon = message.Content
            .Replace(Environment.NewLine, " ")
            .Replace("\\r\\n", " ")
            .Replace("\\r", " ")
            .Replace("\\n", " ")
            .Replace("\r\n", " ")
            .Replace("\r", " ")
            .Replace("\n", " ")
            .Replace("  ", " ")
            .Trim();

        // 判断指令类型
        if (msgCon == "0")
        {
            await 指令说明Handler(member);
        }
        else if (msgCon == "1" || msgCon == "查分" || msgCon == "余" || msgCon == "余额" || msgCon == "剩余")
        {
            await 查分指令Handler(member);
        }
        else if (msgCon == "2" || msgCon == "路" || msgCon == "路子" || msgCon == "路子图")
        {
            await 发送路子图Handler();
        }
        else if (msgCon == "3" || msgCon == "图")
        {
            await 发送开奖图Handler();
        }
        else if (msgCon == "4" || msgCon == "回" || msgCon == "回水" || msgCon == "返水" || msgCon == "反水" || msgCon == "上水")
        {
            await RebateHandler(member, message);
        }
        else if (msgCon == "5" || msgCon == "流" || msgCon == "水" || msgCon == "流水")
        {
            await 流水详情Handler(member);
        }
        else if (msgCon == "6" || msgCon == "时间" || msgCon == "封" || msgCon == "开")
        {
            await 查询时间指令Handler(member);
        }
        else if (msgCon == "7" || msgCon == "撤" || msgCon == "撤销" || msgCon == "取消")
        {
            await CancelBetDataHandler(member);
        }
        else if (msgCon == "8")
        {
            await SendTanImageFullHandler();
        }
        else if (msgCon == "9")
        {
            // await SendSettleReportHandler();
        }
        else if ((Regex.Matches(msgCon, "上[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "上[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "上分[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "上分[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "起[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "起[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "起分[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "起分[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "查[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "查[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)))
        {
            await 上分指令Handler(message);
        }
        else if ((Regex.Matches(msgCon, "下[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "下[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "下分[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "下分[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "落[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "落[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "落分[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "落分[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "回[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "回[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "回分[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "回分[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length))
                )
        {
            await 下分指令Handler(message);
        }
        else
        {
            await 下注指令Handler(message);
        }
    }

    #endregion

    #region 指令处理方法

    /// <summary>
    /// 指令说明处理器
    /// </summary>
    private async Task 指令说明Handler(Member member)
    {
        var helpMessage = @"📋 指令说明：完善中...";
        await chatService.SendGroupMessageAsync(helpMessage, member.Account);
        logger.LogInformation(@"发送指令说明给用户: {Account}", member.Account);
    }

    /// <summary>
    /// 查分指令处理器 - 简洁的用户信息查询
    /// 包含：当前余额、有效流水总额、当前盈亏、本期投注项目明细
    /// </summary>
    private async Task 查分指令Handler(Member member)
    {
        try
        {
            logger.LogInformation(@"查分指令被调用，用户: {Account}", member.Account);

            // 1. 获取当前余额
            var balance = member.Balance;

            // 2. 获取有效流水总额
            var validBetAmount = await betRecordService.GetUserValidTurnoverAsync(member.Account);

            // 3. 计算当前盈亏 - 使用新增的方法获取用户总盈亏
            var totalProfitLoss = await betRecordService.GetUserCurrentProfitLossAsync(member.Account);

            // 4. 获取本期投注项目明细
            var currentIssueTime = issueTimeService.GetCurrentCachedIssueTime();
            var currentIssue = currentIssueTime?.Issue ?? "未知";
            string currentIssueBetDetails = "";

            if (currentIssueTime != null)
            {
                var currentIssueBetSummary = await betRecordService.GetUserBetSummaryByIssueAsync(member.Account, currentIssue);

                if (currentIssueBetSummary.Any())
                {
                    // 使用汇总数据的DisplayText属性，简洁显示
                    var betItems = currentIssueBetSummary.Select(s => s.DisplayText);
                    currentIssueBetDetails = string.Join(" ", betItems);
                }
            }


            // 5. 构建简洁的查分消息
            var message = $@"
{EmojiHelper.GetMoneyBag()}: {balance:F0}
                                {EmojiHelper.GetWater()}: {validBetAmount:F}
                                盈:{totalProfitLoss:F2}
                                本期: {currentIssueBetDetails}";

            await chatService.SendGroupMessageAsync(message, member.Account);

            logger.LogInformation(@"发送查分结果给用户: {Account}, 余额: {Balance}, 有效流水: {ValidBet}, 盈亏: {ProfitLoss}",
                member.Account, balance, validBetAmount, totalProfitLoss);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理查分指令时发生异常，用户: {Account}", member.Account);

            // 发送简化的错误信息
            var errorMessage = $@"💰 余额查询
当前余额: {member.Balance:F2}
其他信息查询失败，请稍后重试";

            await chatService.SendGroupMessageAsync(errorMessage, member.Account);
        }
    }

    /// <summary>
    /// 发送路子图处理器
    /// </summary>
    private async Task 发送路子图Handler()
    {
        try
        {
            logger.LogInformation(@"发送路子图指令被调用");

            // 检查路子图文件是否存在
            var tan7Path = ImageConstants.TanRows7ImagePath;
            var tan6Path = ImageConstants.TanRows6ImagePath;

            if (File.Exists(tan7Path))
            {
                await chatService.SendImageAsync(tan7Path);
                logger.LogInformation(@"发送7行路子图成功: {ImagePath}", tan7Path);
            }
            else if (File.Exists(tan6Path))
            {
                await chatService.SendImageAsync(tan6Path);
                logger.LogInformation(@"发送6行路子图成功: {ImagePath}", tan6Path);
            }
            else
            {
                await chatService.SendGroupMessageAsync("📊 路子图暂未生成，请稍后再试");
                logger.LogWarning(@"路子图文件不存在: {Path7}, {Path6}", tan7Path, tan6Path);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送路子图时发生异常");
            await chatService.SendGroupMessageAsync("📊 发送路子图失败，请稍后再试");
        }
    }

    /// <summary>
    /// 发送开奖图处理器
    /// </summary>
    private async Task 发送开奖图Handler()
    {
        try
        {
            logger.LogInformation(@"发送开奖图指令被调用");

            // 检查开奖图文件是否存在
            var drawImagePath = ImageConstants.DrawImagePath;

            if (File.Exists(drawImagePath))
            {
                await chatService.SendImageAsync(drawImagePath);
                logger.LogInformation(@"发送开奖图成功: {ImagePath}", drawImagePath);
            }
            else
            {
                await chatService.SendGroupMessageAsync("🎯 开奖图暂未生成，请稍后再试");
                logger.LogWarning(@"开奖图文件不存在: {ImagePath}", drawImagePath);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送开奖图时发生异常");
            await chatService.SendGroupMessageAsync("🎯 发送开奖图失败，请稍后再试");
        }
    }

    /// <summary>
    /// 回水处理器 - 执行用户的回水申请
    /// </summary>
    private async Task RebateHandler(Member member, InternalMessage message)
    {
        try
        {
            logger.LogInformation(@"回水指令被调用，用户: {Account}", member.Account);

            var orders = await fSql.Select<BetOrder>()
                .Where(o => o.Account == member.Account &&
                            (o.Status == EnumBetOrderStatus.Win || o.Status == EnumBetOrderStatus.Lose) && // 只计算有效投注
                            o.RebateAmount == 0) // 只处理未回水的订单（回水金额为0）
                .ToListAsync();

            // 如果没有未回水的有效投注
            if (!orders.Any())
            {
                var rebateMessage = $@"{EmojiHelper.GetWater()}: [0] {EmojiHelper.GetMoneyBag()}: {member.Balance:F2}";
                await chatService.SendGroupMessageAsync(rebateMessage, member.Account);
                return;
            }

            // 计算未回水订单的流水和应得回水
            var unpaidTurnover = orders.Sum(o => o.Amount);
            var rebatePercent = member.RebatePercent;
            var currentRebateAmount = unpaidTurnover * (rebatePercent / 100);

            // 如果应得回水金额太小，不发放
            if (currentRebateAmount < 0.01m)
            {
                await chatService.SendGroupMessageAsync($@"💧 本次回水金额过小（{currentRebateAmount:F2}元），暂不发放", member.Account);
                return;
            }

            // 执行回水发放
            // 将string类型的message.Id转换为long类型的哈希值
            var referenceId = Math.Abs(message.Id.GetHashCode());
            var rebateSuccess = await financialService.IncreaseBalanceAsync(
                member.Account, currentRebateAmount, @"Rebate", referenceId,
                $@"回水发放，本次有效流水: {unpaidTurnover:F2}, 回水比例: {rebatePercent:F1}%, 订单数: {orders.Count}",
                @"System", @"用户申请回水");

            if (rebateSuccess)
            {
                // 标记这些订单为已回水，并记录回水金额和时间
                await MarkOrdersAsRebatePaidAsync(orders, rebatePercent);

                // 获取当前用户
                var currentMember = await memberService.GetMemberAsync(member.Account);
                var rebateMessage = $@"{EmojiHelper.GetWater()}: [{currentRebateAmount:F2}] {EmojiHelper.GetMoneyBag()}: {currentMember?.Balance:F2}";
                await chatService.SendGroupMessageAsync(rebateMessage, member.Account);

                logger.LogInformation(@"回水发放成功，用户: {Account}, 本次流水: {Turnover}, 本次回水: {Rebate}, 订单数: {Count}",
                    member.Account, unpaidTurnover, currentRebateAmount, orders.Count);
            }
            else
            {
                await chatService.SendGroupMessageAsync("💧 回水发放失败，请联系客服", member.Account);
                logger.LogError(@"回水发放失败，用户: {Account}, 回水金额: {Rebate}", member.Account, currentRebateAmount);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理回水申请时发生异常，用户: {Account}", member.Account);
            await chatService.SendGroupMessageAsync("💧 回水申请失败，请稍后再试", member.Account);
        }
    }

    /// <summary>
    /// 流水详情处理器 - 显示用户的完整流水统计信息
    /// 包含：有效流水总额、已回水流水、未回水流水、上下分记录、盈亏统计、当前余额
    /// </summary>
    private async Task 流水详情Handler(Member member)
    {
        try
        {
            logger.LogInformation(@"流水详情指令被调用，用户: {Account}", member.Account);


            // 1. 获取所有有效流水总额（今日💦）
            var todayBetRecords = await betRecordService.QueryBetRecordsAsync(DateTime.MinValue, DateTime.MaxValue, null, member.Account);
            var totalValidTurnover = betRecordService.CalculateValidBetAmount(todayBetRecords);

            // 2. 计算今日已回水和未回水流水
            var paidRebateValidTurnover = todayBetRecords.Where(r => r.RebateAmount > 0 && r.IsValidBet).Sum(r => r.Amount);
            var unpaidRebateValidTurnover = totalValidTurnover - paidRebateValidTurnover;

            // 3. 获取所有上下分金额（今日起💰 和 今日落💰）
            var todayDepositWithdrawRecords = await depositWithdrawRecordService.QueryDepositWithdrawRecordsAsync(DateTime.MinValue, DateTime.MaxValue, member.Account);
            var totalDeposit = todayDepositWithdrawRecords.Where(r => r.Type == "上分" && r.Status == EnumDepositStatus.Approved).Sum(r => r.Amount);
            var totalWithdraw = todayDepositWithdrawRecords.Where(r => r.Type == "下分" && r.Status == EnumDepositStatus.Approved).Sum(r => r.Amount);

            // 4. 计算当前盈亏（今日-+）- 使用新增的方法获取用户总盈亏
            var totalProfitLoss = await betRecordService.GetUserCurrentProfitLossAsync(member.Account);

            // 6. 获取当前余额（还有💰）
            var currentBalance = member.Balance;

            // 7. 构建流水详情消息
            var flowMessage = $@"
                                今日💦:{totalValidTurnover:F0}
                                已取💦:{paidRebateValidTurnover:F0}
                                未取💦:{unpaidRebateValidTurnover:F0}
                                今日起💰:{totalDeposit:F0}
                                今日落💰:{totalWithdraw:F0}
                                今日-+:{totalProfitLoss:F2}
                                还有💰:{currentBalance:F2}";

            await chatService.SendGroupMessageAsync(flowMessage, member.Account);

            logger.LogInformation(@"发送流水详情给用户: {Account}, 有效流水: {ValidTurnover}, 未回水: {UnpaidTurnover}, 盈亏: {ProfitLoss}",
                member.Account, totalValidTurnover, unpaidRebateValidTurnover, totalProfitLoss);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理流水详情查询时发生异常，用户: {Account}", member.Account);
            await chatService.SendGroupMessageAsync("📈 流水详情查询失败，请稍后再试", member.Account);
        }
    }

    /// <summary>
    /// 查询时间指令处理器
    /// </summary>
    private async Task 查询时间指令Handler(Member member)
    {
        try
        {
            logger.LogInformation(@"查询时间指令被调用，用户: {Account}", member.Account);

            // 获取当前期号信息
            var currentIssueTime = issueTimeService.GetCurrentCachedIssueTime();
            var currentTime = DateTime.Now;

            if (currentIssueTime == null)
            {
                var message = $@"⏰ 当前时间: {currentTime:yyyy-MM-dd HH:mm:ss}
{EmojiHelper.GetCrossMark()} 期号信息暂未加载";
                await chatService.SendGroupMessageAsync(message, member.Account);
                return;
            }

            // 获取开盘和封盘倒计时
            var openTimeSpan = issueTimeService.GetCurrentCachedOpenTimeSpan();
            var closeTimeSpan = issueTimeService.GetCurrentCachedCloseTimeSpan();

            // 判断当前状态
            string status;
            string timeInfo;

            if (openTimeSpan > 0)
            {
                status = "🔒 封盘中";
                timeInfo = $"开盘倒计时: {TimeHelper.FormatTimeRemaining(openTimeSpan)}";
            }
            else if (closeTimeSpan > 0)
            {
                status = "🟢 开盘中";
                timeInfo = $"封盘倒计时: {TimeHelper.FormatTimeRemaining(closeTimeSpan)}";
            }
            else
            {
                status = "🔒 封盘中";
                timeInfo = "等待下期开盘";
            }

            var timeMessage = $@"⏰ 时间查询
当前时间: {currentTime:yyyy-MM-dd HH:mm:ss}
当前期号: {currentIssueTime.Issue}
当前状态: {status}
{timeInfo}";

            await chatService.SendGroupMessageAsync(timeMessage, member.Account);
            logger.LogInformation(@"发送时间查询结果给用户: {Account}, 期号: {Issue}, 状态: {Status}",
                member.Account, currentIssueTime.Issue, status);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理时间查询时发生异常，用户: {Account}", member.Account);
            await chatService.SendGroupMessageAsync("⏰ 时间查询失败，请稍后再试", member.Account);
        }
    }

    /// <summary>
    /// 撤销投注处理器
    /// </summary>
    private async Task CancelBetDataHandler(Member member)
    {
        try
        {
            logger.LogInformation(@"撤销投注指令被调用，用户: {Account}", member.Account);

            // 获取当前期号
            var currentIssueTime = issueTimeService.GetCurrentCachedIssueTime();
            if (currentIssueTime == null)
            {
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 当前期号信息不可用，无法撤销投注", member.Account);
                return;
            }

            // 查询当前期该用户的未结算投注订单
            var pendingBetOrders = await fSql.Select<BetOrder>()
                .Where(o => o.Account == member.Account &&
                            o.Issue == currentIssueTime.Issue &&
                            o.Status == EnumBetOrderStatus.Confirmed)
                .ToListAsync();

            if (!pendingBetOrders.Any())
            {
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 当前期暂无可撤销的投注", member.Account);
                return;
            }

            // 检查是否还在投注时间内（可以撤销）
            var now = DateTime.Now;
            if (now >= currentIssueTime.CloseTime)
            {
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 已封盘，无法撤销投注", member.Account);
                return;
            }

            // 批量撤销所有未结算的投注
            int cancelledCount = 0;
            decimal refundAmount = 0;

            foreach (var betOrder in pendingBetOrders)
            {
                var success = await betRecordService.CancelBetOrderAsync(betOrder.Id, "用户撤销", "用户主动撤销投注");
                if (success)
                {
                    cancelledCount++;
                    refundAmount += betOrder.Amount;
                }
            }

            if (cancelledCount > 0)
            {
                var cancelMessage = $@"✅ 撤销成功
撤销订单: {cancelledCount} 个
退还金额: {refundAmount:F2}";

                await chatService.SendGroupMessageAsync(cancelMessage, member.Account);
                logger.LogInformation(@"用户撤销答题成功，用户: {Account}, 期号: {Issue}, 撤销数量: {Count}, 退还金额: {Amount}",
                    member.Account, currentIssueTime.Issue, cancelledCount, refundAmount);
            }
            else
            {
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 撤销失败，请稍后再试", member.Account);
                logger.LogWarning(@"用户撤销答题失败，用户: {Account}, 期号: {Issue}", member.Account, currentIssueTime.Issue);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理撤销答题时发生异常，用户: {Account}", member.Account);
            await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 撤销答题失败，请稍后再试", member.Account);
        }
    }

    /// <summary>
    /// 发送摊图处理器
    /// </summary>
    private async Task SendTanImageFullHandler()
    {
        try
        {
            logger.LogInformation(@"发送摊图指令被调用");

            // 检查完整摊图文件是否存在
            var tan77Path = ImageConstants.TanRows77ImagePath;
            var tan66Path = ImageConstants.TanRows66ImagePath;

            if (File.Exists(tan77Path))
            {
                await chatService.SendImageAsync(tan77Path);
                logger.LogInformation(@"发送7行完整摊图成功: {ImagePath}", tan77Path);
            }
            else if (File.Exists(tan66Path))
            {
                await chatService.SendImageAsync(tan66Path);
                logger.LogInformation(@"发送6行完整摊图成功: {ImagePath}", tan66Path);
            }
            else
            {
                await chatService.SendGroupMessageAsync("🎲 完整摊图暂未生成，请稍后再试");
                logger.LogWarning(@"完整摊图文件不存在: {Path77}, {Path66}", tan77Path, tan66Path);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送完整摊图时发生异常");
            await chatService.SendGroupMessageAsync("🎲 发送完整摊图失败，请稍后再试");
        }
    }

    /// <summary>
    /// 上分指令处理器
    /// </summary>
    private async Task 上分指令Handler(InternalMessage message)
    {
        try
        {
            // 使用正则表达式提取消息中的数字金额
            var match = Regex.Match(message.Content, @"(\d+)", RegexOptions.IgnoreCase);
            if (match.Success && decimal.TryParse(match.Groups[1].Value, out var amount))
            {
                try
                {
                    // 获取会员信息（此时会员应该已存在）
                    var member = await memberService.GetMemberAsync(message.Account);
                    if (member == null)
                    {
                        await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 用户信息不存在，无法处理上分申请", message.Account);
                        logger.LogWarning(@"用户不存在，无法处理上分申请，账号: {Account}", message.Account);
                        return;
                    }

                    // 检查用户是否有未处理的上分申请
                    var hasPendingDeposit = await depositWithdrawRecordService.HasPendingDepositRequestAsync(message.Account);
                    if (hasPendingDeposit)
                    {
                        await chatService.SendGroupMessageAsync("上下分发送一次就可以! 勿催", message.Account);
                        logger.LogInformation(@"用户有未处理的上分申请，拒绝新申请，账号: {Account}", message.Account);
                        return;
                    }

                    // 立即反馈收到申请
                    await chatService.SendGroupMessageAsync($"已收到[{message.Content}]请求", message.Account);
                    logger.LogInformation(@"已向用户发送上分申请收到反馈，账号: {Account}, 金额: {Amount}", message.Account, amount);

                    // 播放充值提现音效
                    _ = Task.Run(async () => await audioService.PlayRechargeWithdrawalAsync());

                    // 创建上分申请记录
                    var requestId = await depositWithdrawRecordService.CreateDepositRequestAsync(message.Account, amount, message.Content, message.Id);

                    if (requestId > 0)
                    {
                        logger.LogInformation(@"创建上分申请成功，用户: {Account}, 金额: {Amount}, 申请ID: {RequestId}",
                            message.Account, amount, requestId);
                    }
                    else
                    {
                        // 创建申请失败，发送错误反馈
                        await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 上分申请创建失败，请稍后重试或联系客服", message.Account);
                        logger.LogError(@"创建上分申请失败，用户: {Account}, 金额: {Amount}", message.Account, amount);
                    }
                }
                catch (Exception ex)
                {
                    // 处理异常，发送错误反馈
                    await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 上分申请处理失败，请稍后重试或联系客服", message.Account);
                    logger.LogError(ex, @"处理上分申请时发生异常，用户: {Account}, 金额: {Amount}", message.Account, amount);
                }
            }
            else
            {
                // 金额解析失败，发送格式错误反馈
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 上分指令格式错误，请使用正确格式，如：上100", message.Account);
                logger.LogWarning(@"上分指令格式错误，用户: {Account}, 内容: {Content}", message.Account, message.Content);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理上分指令时发生异常，用户: {Account}", message.Account);
            await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 上分申请处理失败，请稍后重试或联系客服", message.Account);
        }
    }

    /// <summary>
    /// 下分指令处理器
    /// </summary>
    private async Task 下分指令Handler(InternalMessage message)
    {
        try
        {
            // 使用正则表达式提取消息中的数字金额
            var match = Regex.Match(message.Content, @"(\d+)", RegexOptions.IgnoreCase);
            if (match.Success && decimal.TryParse(match.Groups[1].Value, out var amount))
            {
                try
                {
                    // 获取会员信息（此时会员应该已存在）
                    var member = await memberService.GetMemberAsync(message.Account);
                    if (member == null)
                    {
                        await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 用户信息不存在，无法处理下分申请", message.Account);
                        logger.LogWarning(@"用户不存在，无法处理下分申请，账号: {Account}", message.Account);
                        return;
                    }

                    // 检查用户是否有未处理的下分申请
                    var hasPendingWithdraw = await depositWithdrawRecordService.HasPendingWithdrawRequestAsync(message.Account);
                    if (hasPendingWithdraw)
                    {
                        await chatService.SendGroupMessageAsync("上下分发送一次就可以! 勿催", message.Account);
                        logger.LogInformation(@"用户有未处理的下分申请，拒绝新申请，账号: {Account}", message.Account);
                        return;
                    }

                    // 检查余额是否充足
                    if (member.Balance < amount)
                    {
                        await chatService.SendGroupMessageAsync($@"{EmojiHelper.GetCrossMark()} 余额不足，无法申请下分
当前余额: {member.Balance:F2}
申请金额: {amount:F2}
请检查余额后重新申请", message.Account);
                        logger.LogWarning(@"用户余额不足，无法处理下分申请，账号: {Account}, 余额: {Balance}, 申请金额: {Amount}",
                            message.Account, member.Balance, amount);
                        return;
                    }

                    // 立即反馈收到申请
                    await chatService.SendGroupMessageAsync($"已收到[{message.Content}]请求", message.Account);
                    logger.LogInformation(@"已向用户发送下分申请收到反馈，账号: {Account}, 金额: {Amount}", message.Account, amount);

                    // 播放充值提现音效
                    _ = Task.Run(async () => await audioService.PlayRechargeWithdrawalAsync());

                    // 创建下分申请记录
                    var requestId = await depositWithdrawRecordService.CreateWithdrawRequestAsync(message.Account, amount, message.Content, message.Id);

                    if (requestId > 0)
                    {
                        logger.LogInformation(@"创建下分申请成功，用户: {Account}, 金额: {Amount}, 申请ID: {RequestId}",
                            message.Account, amount, requestId);
                    }
                    else
                    {
                        // 创建申请失败，发送错误反馈
                        await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 下分申请创建失败，请稍后重试或联系客服", message.Account);
                        logger.LogError(@"创建下分申请失败，用户: {Account}, 金额: {Amount}", message.Account, amount);
                    }
                }
                catch (Exception ex)
                {
                    // 处理异常，发送错误反馈
                    await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 下分申请处理失败，请稍后重试或联系客服", message.Account);
                    logger.LogError(ex, @"处理下分申请时发生异常，用户: {Account}, 金额: {Amount}", message.Account, amount);
                }
            }
            else
            {
                // 金额解析失败，发送格式错误反馈
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 下分指令格式错误，请使用正确格式，如：下100", message.Account);
                logger.LogWarning(@"下分指令格式错误，用户: {Account}, 内容: {Content}", message.Account, message.Content);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理下分指令时发生异常，用户: {Account}", message.Account);
        }
    }

    /// <summary>
    /// 下注指令处理器
    /// </summary>
    private async Task 下注指令Handler(InternalMessage message)
    {
        try
        {
            // 获取会员信息（此时会员应该已存在）
            var member = await memberService.GetMemberAsync(message.Account);

            // 分割指令
            string[] commands = Ai.Split(message.Content, " ");

            // 从赔率表获取支持的投注项目列表
            var supportedPlayItems = await oddsService.GetSupportedPlayItemsAsync();
            if (!supportedPlayItems.Any())
            {
                logger.LogWarning(@"未找到支持的投注项目配置，跳过投注处理，用户: {Account}", message.Account);

                return;
            }

            // 第一阶段：验证所有投注指令的格式和项目有效性
            var validatedCommands = new List<(string originalCommand, string processedCommand, string playItem, decimal amount)>();
            var validationErrors = new List<(string command, string errorMessage)>();

            foreach (string command in commands)
            {
                // 修正指令并去除前后空格
                string newCommand = ChangeCommand(command).Trim();

                // 验证投注指令
                var (isValid, playItem, amount, errorMessage) = await betCommandValidatorService.ValidateBetCommandAsync(newCommand);
                if (isValid)
                {
                    validatedCommands.Add((command, newCommand, playItem, amount));
                }
                else
                {
                    validationErrors.Add((command, errorMessage));
                }
            }

            // 如果有验证失败的指令，根据错误类型给出具体提示
            if (validationErrors.Count > 0)
            {
                // 按错误类型分组，提供更精确的错误提示
                var errorGroups = validationErrors.GroupBy(e => GetErrorCategory(e.errorMessage));
                var errorMessages = new List<string>();

                foreach (var group in errorGroups)
                {
                    var errorCommands = group.Select(g => g.command).ToList();
                    var commandsText = string.Join("、", errorCommands);

                    switch (group.Key)
                    {
                        case "格式错误":
                            errorMessages.Add($"格式错误：{commandsText}（正确格式：项目/金额，如：大/100）");
                            break;
                        case "金额格式错误":
                            errorMessages.Add($"金额格式错误：{commandsText}（金额必须是数字）");
                            break;
                        case "金额无效":
                            errorMessages.Add($"金额无效：{commandsText}（金额必须大于0）");
                            break;
                        case "金额超限":
                            errorMessages.Add($"金额超限：{commandsText}（请检查最小/最大限额）");
                            break;
                        case "项目不支持":
                            errorMessages.Add($"不支持的答题项目：{commandsText}");
                            break;
                        default:
                            errorMessages.Add($"验证失败：{commandsText}");
                            break;
                    }
                }

                string fullErrorMessage = $"{EmojiHelper.GetCrossMark()}答题失败：{string.Join("；", errorMessages)}，全部答题不予受理";
                await chatService.SendGroupMessageAsync(fullErrorMessage, message.Account);
                logger.LogWarning(@"包含验证失败的指令，全部不予受理，用户: {Account}, 消息: {Content}, 错误详情: {Errors}",
                    message.Account, message.Content, string.Join("; ", validationErrors.Select(e => $"{e.command}:{e.errorMessage}")));
                return;
            }

            // 如果没有有效的投注指令，直接返回
            if (validatedCommands.Count == 0)
            {
                return;
            }

            // 第二阶段：计算总投注金额并检查余额是否充足
            var totalAmount = validatedCommands.Sum(cmd => cmd.amount);
            var currentBalance = await financialService.GetCurrentBalanceAsync(message.Account);

            if (currentBalance < totalAmount)
            {
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 答题失败：余额不足，所需总额: {totalAmount:F2}，当前可用积分: {currentBalance:F2}，全部答题不予受理", message.Account);
                logger.LogWarning(@"余额不足，全部投注不予受理，用户: {Account}, 所需总额: {TotalAmount:F2}, 当前余额: {CurrentBalance:F2}",
                    message.Account, totalAmount, currentBalance);
                return;
            }

            // 获取当前期号
            var currentIssue = issueTimeService.GetCurrentCachedIssueTime();
            if (currentIssue == null)
            {
                logger.LogWarning(@"无法获取当前期号，跳过投注处理，用户: {Account}", message.Account);
                return;
            }

            // 第三阶段：检查单期限额
            var singleIssueLimit = await systemSettingService.GetSingleIssueLimitAsync();
            var currentIssueBetAmount = await betRecordService.GetUserCurrentIssueBetAmountAsync(message.Account, currentIssue.Issue);
            var newTotalBetAmount = currentIssueBetAmount + totalAmount;

            if (newTotalBetAmount > singleIssueLimit)
            {
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 答题失败：超出单期限额，当期已答题: {currentIssueBetAmount:F2}，本次答题: {totalAmount:F2}，单期限额: {singleIssueLimit:F2}，全部答题不予受理", message.Account);
                logger.LogWarning(@"超出单期限额，全部投注不予受理，用户: {Account}, 当期已投注: {CurrentBet:F2}, 本次投注: {NewBet:F2}, 单期限额: {Limit:F2}",
                    message.Account, currentIssueBetAmount, totalAmount, singleIssueLimit);
                return;
            }

            // 第四阶段：检查投注项目总限额（TotalStake）
            var totalStakeViolations = new List<string>();
            foreach (var validatedCommand in validatedCommands)
            {
                // 获取当前期该投注项目的总投注额
                var currentPlayItemTotalBet = await betRecordService.GetCurrentIssuePlayItemTotalBetAsync(currentIssue.Issue, validatedCommand.playItem);

                // 获取该投注项目的赔率配置（包含总限额）
                var oddsConfig = await oddsService.GetOddsByPlayItemAsync(validatedCommand.playItem);
                if (oddsConfig == null)
                {
                    logger.LogWarning(@"无法获取投注项目 {PlayItem} 的赔率配置", validatedCommand.playItem);
                    continue;
                }

                // 检查是否超过投注项目总限额
                var newPlayItemTotalBet = currentPlayItemTotalBet + validatedCommand.amount;
                if (newPlayItemTotalBet > oddsConfig.TotalStake)
                {
                    var remainingLimit = Math.Max(0, oddsConfig.TotalStake - currentPlayItemTotalBet);
                    totalStakeViolations.Add($"{validatedCommand.playItem}(剩余可投: {remainingLimit:F0})");
                }
            }

            // 如果有投注项目超过总限额，拒绝所有投注
            if (totalStakeViolations.Count > 0)
            {
                var violationText = string.Join("、", totalStakeViolations);
                await chatService.SendGroupMessageAsync($"{EmojiHelper.GetCrossMark()} 答题失败：以下项目超出总限额，{violationText}，全部答题不予受理", message.Account);
                logger.LogWarning(@"投注项目超出总限额，全部投注不予受理，用户: {Account}, 违规项目: {Violations}",
                    message.Account, violationText);
                return;
            }

            // 重新获取当前期号（因为上面的检查可能耗时）
            currentIssue = issueTimeService.GetCurrentCachedIssueTime();
            if (currentIssue == null)
            {
                logger.LogWarning(@"无法获取当前期号，跳过投注处理，用户: {Account}", message.Account);

                return;
            }

            // 检查全局投注状态 - 统一投注控制入口
            // RuntimeConfiguration.CanBet综合考虑：时间+上期开奖状态+游戏服务状态
            if (!RuntimeConfiguration.CanBet)
            {
                logger.LogWarning(@"当前不可答题（未开始答题或等待上期开奖），用户: {Account}, 期号: {Issue}", message.Account, currentIssue.Issue);

                // 发送用户友好的提醒消息
                var reminderMessage = $"{EmojiHelper.GetCrossMark()}已封卷\n{message.Content}\n无效";
                await chatService.SendGroupMessageAsync(reminderMessage, member!.Account);
                return;
            }

            // 第三阶段：批量创建投注订单（此时已确保所有指令有效且余额充足）
            bool hasSuccessfulBet = false;
            var failedOrders = new List<string>();

            foreach (var (originalCommand, processedCommand, playItem, amount) in validatedCommands)
            {
                try
                {
                    // 获取赔率配置
                    var oddsConfig = await oddsService.GetOddsByPlayItemAsync(playItem);
                    if (oddsConfig == null)
                    {
                        logger.LogWarning(@"答题项目配置不存在，跳过，项目: {PlayItem}", playItem);
                        failedOrders.Add(originalCommand);
                        continue;
                    }

                    // 创建答题订单
                    var (orderId, betErrorMessage) = await betRecordService.CreateBetOrderAsync(
                        message.Account, member?.NickName ?? message.NickName, currentIssue.Issue, playItem, amount, oddsConfig.Odds, message.Content, message.Id);

                    if (orderId > 0)
                    {
                        hasSuccessfulBet = true;
                        logger.LogInformation(@"创建投注订单成功，用户: {Account}, 期号: {Issue}, 项目: {PlayItem}, 金额: {Amount}, 赔率: {Odds}, 订单ID: {OrderId}",
                            message.Account, currentIssue.Issue, playItem, amount, oddsConfig.Odds, orderId);
                    }
                    else
                    {
                        // 理论上不应该到这里，因为已经预检查了余额
                        logger.LogError(@"预检查通过但创建投注订单失败，用户: {Account}, 项目: {PlayItem}, 金额: {Amount}, 错误: {Error}",
                            message.Account, playItem, amount, betErrorMessage);
                        failedOrders.Add(originalCommand);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, @"处理投注指令异常，指令: {Command}", originalCommand);
                    failedOrders.Add(originalCommand);
                }
            }

            // 如果有失败的订单，记录日志
            if (failedOrders.Count > 0)
            {
                logger.LogWarning(@"部分投注订单创建失败，用户: {Account}, 失败指令: {FailedCommands}",
                    message.Account, string.Join(", ", failedOrders));
            }

            // 反馈答题结果
            if (hasSuccessfulBet)
            {
                // 使用新的方法获取按答题项目合并的答题汇总
                var betSummary = await betRecordService.GetUserBetSummaryByIssueAsync(message.Account, currentIssue.Issue);

                // 如果没有答题汇总数据
                if (!betSummary.Any())
                {
                    logger.LogWarning(@"获取答题汇总数据为空，用户: {Account}, 期号: {Issue}", message.Account, currentIssue.Issue);
                    return;
                }

                // 获取投注后的当前余额
                var balanceAfterBet = await financialService.GetCurrentBalanceAsync(message.Account);

                // 使用BetItemSummary的DisplayText属性生成反馈消息
                var betItems = betSummary.Select(s => s.DisplayText);
                string betItemsText = Ai.中括号左 + string.Join(" ", betItems) + Ai.中括号右;
                string successMessage = $"{EmojiHelper.GetCheckMark()}成功\n{betItemsText}\n{EmojiHelper.GetMoneyBag()}: {balanceAfterBet:F2}";

                await chatService.SendGroupMessageAsync(successMessage, member!.Account);
                logger.LogInformation(@"答题消息处理完成，用户: {Account}, 消息ID: {MessageId}, 答题项目数: {ItemCount}, 投注后余额: {Balance}",
                    message.Account, message.Id, betSummary.Count, balanceAfterBet);
            }
            else
            {
                logger.LogWarning(@"投注消息处理失败，没有成功创建任何订单，用户: {Account}, 消息ID: {MessageId}", message.Account, message.Id);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理下注指令时发生异常，用户: {Account}", message.Account);
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 修正投注指令格式 - 标准化用户输入
    ///
    /// 功能：
    /// - 将各种分隔符统一为"/"
    /// - 支持中英文标点符号
    /// - 标准化投注指令格式
    ///
    /// 转换规则：
    /// - "." → "/"
    /// - "。" → "/"
    /// - "，" → "/"
    /// - "," → "/"
    ///
    /// 示例：
    /// - "大10.小20" → "大10/小20"
    /// - "单15，双25" → "单15/双25"
    /// </summary>
    /// <param name="command">原始投注指令</param>
    /// <returns>标准化后的投注指令</returns>
    private string ChangeCommand(string command)
    {
        // 修正分隔符
        command = command.Replace(".", "/");
        command = command.Replace("。", "/");
        command = command.Replace("，", "/");
        command = command.Replace(",", "/");
        command = command.Replace("?", "/");
        command = command.Replace("=", "/");
        command = command.Replace("+", "/");
        command = command.Replace("·", "/");
        command = command.Replace("！", "/");
        command = command.Replace("!", "/");
        command = command.Replace("@", "/");
        command = command.Replace("#", "/");
        command = command.Replace("*", "/");
        command = command.Replace("、", "/");
        command = command.Replace("／", "/");

        // 标准化投注指令格式-正
        command = command.Replace("正", "正/");
        command = command.Replace("堂", "正/");
        command = command.Replace("糖", "正/");
        command = command.Replace("中", "正/");

        // 标准化投注指令格式-番
        command = command.Replace("番", "番/");
        command = command.Replace("高", "番/");
        command = command.Replace("牛", "番/");
        command = command.Replace("风", "番/");
        command = command.Replace("方", "番/");
        command = command.Replace("翻", "番/");
        command = command.Replace(":", "番/");

        // 标准化投注指令格式-角
        command = command.Replace("角", "角/");

        // 标准化投注指令格式-念
        command = command.Replace("粘", "念");
        command = command.Replace("贴", "念");
        command = command.Replace("严", "念");
        command = command.Replace("那", "念");
        command = command.Replace("拿", "念");
        command = command.Replace("拖", "念");
        command = command.Replace("-", "念");
        if (command.Contains("念") && command.Length > 4)
        {
            command = command.Substring(0, 3) + "/" + command.Substring(3, command.Length - 3);
        }

        // 标准化投注指令格式-单双
        command = command.Replace("单", "单/");
        command = command.Replace("13角", "单/");
        command = command.Replace("31角", "单/");
        command = command.Replace("双", "双/");
        command = command.Replace("24角", "双/");
        command = command.Replace("42角", "双/");

        // 标准化投注指令格式-大小
        //command = command.Replace("大", "大/");
        //command = command.Replace("小", "小/");

        // 去除多余的分隔符
        command = command.Replace("//", "/");

        // 标准化投注指令格式-数字
        if (Ai.GetTextLeft(command, "/") == "1") command = command.Replace("1/", "1正/");
        if (Ai.GetTextLeft(command, "/") == "2") command = command.Replace("2/", "2正/");
        if (Ai.GetTextLeft(command, "/") == "3") command = command.Replace("3/", "3正/");
        if (Ai.GetTextLeft(command, "/") == "4") command = command.Replace("4/", "4正/");
        if (Ai.GetTextLeft(command, "/") == "12") command = command.Replace("12/", "12角/");
        if (Ai.GetTextLeft(command, "/") == "21") command = command.Replace("21/", "12角/");
        if (Ai.GetTextLeft(command, "/") == "23") command = command.Replace("23/", "23角/");
        if (Ai.GetTextLeft(command, "/") == "32") command = command.Replace("32/", "23角/");
        if (Ai.GetTextLeft(command, "/") == "34") command = command.Replace("34/", "34角/");
        if (Ai.GetTextLeft(command, "/") == "43") command = command.Replace("43/", "34角/");
        if (Ai.GetTextLeft(command, "/") == "14") command = command.Replace("14/", "14角/");
        if (Ai.GetTextLeft(command, "/") == "41") command = command.Replace("41/", "14角/");
        if (Ai.GetTextLeft(command, "/") == "31") command = command.Replace("31/", "单/");
        if (Ai.GetTextLeft(command, "/") == "13") command = command.Replace("13/", "单/");
        if (Ai.GetTextLeft(command, "/") == "24") command = command.Replace("24/", "双/");
        if (Ai.GetTextLeft(command, "/") == "42") command = command.Replace("42/", "双/");
        if (Ai.GetTextLeft(command, "/") == "1122") command = command.Replace("1122/", "小/");
        if (Ai.GetTextLeft(command, "/") == "2211") command = command.Replace("2211/", "小/");
        if (Ai.GetTextLeft(command, "/") == "3344") command = command.Replace("3344/", "大/");
        if (Ai.GetTextLeft(command, "/") == "4433") command = command.Replace("4433/", "大/");
        // if (Ai.GetTextLeft(command, "/") == "123") command = command.Replace("123/", "三门123/");
        // if (Ai.GetTextLeft(command, "/") == "124") command = command.Replace("124/", "三门124/");
        // if (Ai.GetTextLeft(command, "/") == "134") command = command.Replace("134/", "三门134/");
        // if (Ai.GetTextLeft(command, "/") == "234") command = command.Replace("234/", "三门234/");
        // if (Ai.GetTextLeft(command, "/") == "152") command = command.Replace("152/", "1无2/");
        // if (Ai.GetTextLeft(command, "/") == "153") command = command.Replace("153/", "1无3/");
        // if (Ai.GetTextLeft(command, "/") == "154") command = command.Replace("154/", "1无4/");
        // if (Ai.GetTextLeft(command, "/") == "251") command = command.Replace("251/", "2无1/");
        // if (Ai.GetTextLeft(command, "/") == "253") command = command.Replace("253/", "2无3/");
        // if (Ai.GetTextLeft(command, "/") == "254") command = command.Replace("254/", "2无4/");
        // if (Ai.GetTextLeft(command, "/") == "351") command = command.Replace("351/", "3无1/");
        // if (Ai.GetTextLeft(command, "/") == "352") command = command.Replace("352/", "3无2/");
        // if (Ai.GetTextLeft(command, "/") == "354") command = command.Replace("354/", "3无4/");
        // if (Ai.GetTextLeft(command, "/") == "451") command = command.Replace("451/", "4无1/");
        // if (Ai.GetTextLeft(command, "/") == "452") command = command.Replace("452/", "4无2/");
        // if (Ai.GetTextLeft(command, "/") == "453") command = command.Replace("453/", "4无3/");
        return command;
    }

    /// <summary>
    /// 标记订单为已回水
    /// </summary>
    /// <param name="orders">要标记的订单列表</param>
    /// <param name="rebatePercent">回水比例</param>
    private async Task MarkOrdersAsRebatePaidAsync(List<BetOrder> orders, decimal rebatePercent)
    {
        try
        {
            var now = DateTime.Now;

            foreach (var order in orders)
            {
                // 计算并设置回水金额（>0表示已回水）
                order.RebateAmount = order.Amount * (rebatePercent / 100);
                order.RebatePaidTime = now;
            }

            // 批量更新订单状态
            await fSql.Update<BetOrder>()
                .SetSource(orders)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            logger.LogInformation(@"标记 {Count} 个订单为已回水", orders.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"标记订单为已回水失败");
            throw;
        }
    }

    /// <summary>
    /// 根据错误消息获取错误分类
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>错误分类</returns>
    private static string GetErrorCategory(string errorMessage)
    {
        if (string.IsNullOrWhiteSpace(errorMessage))
            return "未知错误";

        // 指令格式错误
        if (errorMessage.Contains("投注指令格式错误") || errorMessage.Contains("格式错误，应为"))
            return "格式错误";

        // 金额格式错误
        if (errorMessage.Contains("金额格式错误") || errorMessage.Contains("金额必须是数字"))
            return "金额格式错误";

        // 金额数值无效
        if (errorMessage.Contains("金额必须大于0"))
            return "金额无效";

        // 金额超出限额
        if (errorMessage.Contains("不能小于最小限额") || errorMessage.Contains("不能大于最大限额"))
            return "金额超限";

        // 投注项目不支持
        if (errorMessage.Contains("不支持的投注项目"))
            return "项目不支持";

        return "其他错误";
    }

    #endregion
}